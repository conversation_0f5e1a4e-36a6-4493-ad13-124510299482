// Simple test to verify trial conversion works with successful orders
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testTrialConversion() {
  console.log('🧪 Testing trial conversion functionality...\n');

  try {
    // Test 1: Create account with active trial and test successful order conversion
    console.log('Test 1: Active trial → Successful order → Should convert to paid');
    
    const testAccountId = '123e4567-e89b-12d3-a456-************';
    
    // Create test account with active trial
    const { error: insertError } = await supabase
      .from('accounts')
      .upsert({
        id: testAccountId,
        name: 'Test Account Active Trial',
        slug: 'test-account-active',
        trial_status: 'active',
        trial_started_at: new Date().toISOString(),
        trial_ends_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      });

    if (insertError) {
      console.error('❌ Failed to create test account:', insertError);
      return;
    }

    // Check initial trial status
    const { data: beforeData } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId)
      .single();

    console.log(`   Before order: trial_status = ${beforeData?.trial_status}`);

    // Create successful order using upsert_order function
    const { data: orderResult, error: orderError } = await supabase.rpc('upsert_order', {
      target_account_id: testAccountId,
      target_customer_id: 'test_customer_123',
      target_order_id: 'order_123',
      status: 'succeeded',
      billing_provider: 'stripe',
      total_amount: 2999,
      currency: 'USD',
      line_items: [
        {
          id: 'item_1',
          product_id: 'prod_123',
          variant_id: 'var_123',
          price_amount: 2999,
          quantity: 1
        }
      ]
    });

    if (orderError) {
      console.error('❌ Failed to create order:', orderError);
      return;
    }

    // Check trial status after successful order
    const { data: afterData } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId)
      .single();

    console.log(`   After order: trial_status = ${afterData?.trial_status}`);
    
    if (afterData?.trial_status === 'converted') {
      console.log('✅ Test 1 PASSED: Trial status converted to paid\n');
    } else {
      console.log('❌ Test 1 FAILED: Trial status should be "converted"\n');
    }

    // Test 2: Test with expired trial
    console.log('Test 2: Expired trial → Successful order → Should convert to paid');
    
    const testAccountId2 = '123e4567-e89b-12d3-a456-************';
    
    // Create test account with expired trial
    await supabase
      .from('accounts')
      .upsert({
        id: testAccountId2,
        name: 'Test Account Expired Trial',
        slug: 'test-account-expired',
        trial_status: 'expired',
        trial_started_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        trial_ends_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
      });

    // Check initial trial status
    const { data: beforeData2 } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId2)
      .single();

    console.log(`   Before order: trial_status = ${beforeData2?.trial_status}`);

    // Create successful order
    await supabase.rpc('upsert_order', {
      target_account_id: testAccountId2,
      target_customer_id: 'test_customer_124',
      target_order_id: 'order_124',
      status: 'succeeded',
      billing_provider: 'stripe',
      total_amount: 2999,
      currency: 'USD',
      line_items: [
        {
          id: 'item_2',
          product_id: 'prod_124',
          variant_id: 'var_124',
          price_amount: 2999,
          quantity: 1
        }
      ]
    });

    // Check trial status after successful order
    const { data: afterData2 } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId2)
      .single();

    console.log(`   After order: trial_status = ${afterData2?.trial_status}`);
    
    if (afterData2?.trial_status === 'converted') {
      console.log('✅ Test 2 PASSED: Expired trial converted to paid\n');
    } else {
      console.log('❌ Test 2 FAILED: Trial status should be "converted"\n');
    }

    // Test 3: Test with inactive trial (should not change)
    console.log('Test 3: Inactive trial → Successful order → Should remain inactive');
    
    const testAccountId3 = '123e4567-e89b-12d3-a456-************';
    
    // Create test account with inactive trial
    await supabase
      .from('accounts')
      .upsert({
        id: testAccountId3,
        name: 'Test Account Inactive Trial',
        slug: 'test-account-inactive',
        trial_status: 'inactive'
      });

    // Check initial trial status
    const { data: beforeData3 } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId3)
      .single();

    console.log(`   Before order: trial_status = ${beforeData3?.trial_status}`);

    // Create successful order
    await supabase.rpc('upsert_order', {
      target_account_id: testAccountId3,
      target_customer_id: 'test_customer_125',
      target_order_id: 'order_125',
      status: 'succeeded',
      billing_provider: 'stripe',
      total_amount: 2999,
      currency: 'USD',
      line_items: [
        {
          id: 'item_3',
          product_id: 'prod_125',
          variant_id: 'var_125',
          price_amount: 2999,
          quantity: 1
        }
      ]
    });

    // Check trial status after successful order
    const { data: afterData3 } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId3)
      .single();

    console.log(`   After order: trial_status = ${afterData3?.trial_status}`);
    
    if (afterData3?.trial_status === 'inactive') {
      console.log('✅ Test 3 PASSED: Inactive trial remained inactive\n');
    } else {
      console.log('❌ Test 3 FAILED: Trial status should remain "inactive"\n');
    }

    // Test 4: Test pending order doesn't convert trial
    console.log('Test 4: Active trial → Pending order → Should remain active');
    
    const testAccountId4 = '123e4567-e89b-12d3-a456-************';
    
    // Create test account with active trial
    await supabase
      .from('accounts')
      .upsert({
        id: testAccountId4,
        name: 'Test Account Pending Order',
        slug: 'test-account-pending',
        trial_status: 'active',
        trial_started_at: new Date().toISOString(),
        trial_ends_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      });

    // Check initial trial status
    const { data: beforeData4 } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId4)
      .single();

    console.log(`   Before order: trial_status = ${beforeData4?.trial_status}`);

    // Create pending order
    await supabase.rpc('upsert_order', {
      target_account_id: testAccountId4,
      target_customer_id: 'test_customer_126',
      target_order_id: 'order_126',
      status: 'pending',
      billing_provider: 'stripe',
      total_amount: 2999,
      currency: 'USD',
      line_items: [
        {
          id: 'item_4',
          product_id: 'prod_126',
          variant_id: 'var_126',
          price_amount: 2999,
          quantity: 1
        }
      ]
    });

    // Check trial status after pending order
    const { data: afterData4 } = await supabase
      .from('accounts')
      .select('trial_status')
      .eq('id', testAccountId4)
      .single();

    console.log(`   After order: trial_status = ${afterData4?.trial_status}`);
    
    if (afterData4?.trial_status === 'active') {
      console.log('✅ Test 4 PASSED: Active trial remained active for pending order\n');
    } else {
      console.log('❌ Test 4 FAILED: Trial status should remain "active"\n');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

testTrialConversion();
