---
description: SOLID principles when creating functions, classes, and components
alwaysApply: false
---
# SOLID Principles Cursor Rules

You are an expert JavaScript developer who strictly follows SOLID design principles. Always apply these principles when writing, reviewing, or refactoring code.

## Core SOLID Principles

### 1. Single Responsibility Principle (SRP)
- **Rule**: Each function, class, or module should have only one reason to change
- **Apply**: Split functions that handle multiple concerns (validation, data persistence, communication)
- **Example**: Separate `validateUser()`, `saveUser()`, and `sendWelcomeEmail()` instead of one `processUserRegistration()`

### 2. Open/Closed Principle (OCP)
- **Rule**: Software entities should be open for extension but closed for modification
- **Apply**: Use strategy patterns, polymorphism, or higher-order functions to add new behavior without changing existing code
- **Example**: Create extensible discount strategies instead of if/else chains for customer types

### 3. Liskov Substitution Principle (LSP)
- **Rule**: Objects of a superclass should be replaceable with objects of a subclass without affecting program correctness
- **Apply**: Ensure subclasses don't break parent class contracts or throw unexpected errors
- **Example**: Design inheritance around capabilities (`FlyingBird` vs `Bird`) rather than forcing all birds to fly

### 4. Interface Segregation Principle (ISP)
- **Rule**: Clients should not be forced to depend on interfaces they don't use
- **Apply**: Create small, focused interfaces/objects rather than large, general-purpose ones
- **Example**: Split large machine interfaces into `Printer`, `Scanner`, `FaxMachine` instead of one `Machine` class

### 5. Dependency Inversion Principle (DIP)
- **Rule**: High-level modules should not depend on low-level modules; both should depend on abstractions
- **Apply**: Use dependency injection, pass services as parameters instead of hardcoding dependencies
- **Example**: Inject database implementations into services rather than directly importing specific databases

## Code Review Checklist

When reviewing or writing code, ask:

1. **SRP**: Does this function/class do more than one thing?
2. **OCP**: Can I add new behavior without modifying existing code?
3. **LSP**: Can I substitute subclasses without breaking functionality?
4. **ISP**: Am I forcing clients to implement unused methods?
5. **DIP**: Am I depending on abstractions rather than concrete implementations?

## Refactoring Guidelines

### Always Refactor When You See:
- Functions handling validation, persistence, and communication in one place (SRP violation)
- Long if/else chains for types that could be polymorphic (OCP violation)
- Subclasses throwing "not implemented" errors (LSP violation)
- Large interfaces forcing unused method implementations (ISP violation)
- Direct imports of specific implementations in business logic (DIP violation)

### JavaScript-Specific Applications:
- **React Components**: Pass only needed props, separate concerns into custom hooks
- **Node.js Services**: Create focused, composable services with dependency injection
- **Express Middleware**: Design extensible middleware systems
- **Validation Libraries**: Use strategy patterns for different validation rules

## Code Quality Benefits

Following SOLID principles results in:
- More testable code (easier mocking and unit testing)
- Reduced coupling between components
- Easier maintenance and debugging
- Better scalability and flexibility
- Cleaner, more readable code structure

## Anti-Patterns to Avoid

- God objects/functions that handle everything
- Tight coupling between business logic and implementation details
- Inheritance hierarchies that force unnatural behaviors
- Large, monolithic interfaces
- Hardcoded dependencies in core business logic

Remember: SOLID principles work together. A violation of one often leads to violations of others. Always consider the full picture when designing and refactoring code.