-- Test script to verify trial conversion works for successful orders
-- This script tests the updated upsert_order function

-- Test 1: Create account with active trial and test successful order conversion
BEGIN;

-- Create test account with active trial
INSERT INTO public.accounts (id, name, slug, trial_status, trial_started_at, trial_ends_at)
VALUES (
  '123e4567-e89b-12d3-a456-************',
  'Test Account Active Trial',
  'test-account-active',
  'active',
  NOW(),
  NOW() + INTERVAL '7 days'
)
ON CONFLICT (id) DO UPDATE SET
  trial_status = 'active',
  trial_started_at = NOW(),
  trial_ends_at = NOW() + INTERVAL '7 days';

-- Verify account has active trial
SELECT 'Before Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

-- Create successful order using upsert_order function
SELECT public.upsert_order(
  '123e4567-e89b-12d3-a456-************'::uuid,  -- target_account_id
  'test_customer_123',                             -- target_customer_id  
  'order_123',                                     -- target_order_id
  'succeeded'::public.payment_status,              -- status
  'stripe'::public.billing_provider,               -- billing_provider
  2999,                                            -- total_amount (in cents)
  'USD',                                           -- currency
  '[{"id": "item_1", "product_id": "prod_123", "variant_id": "var_123", "price_amount": 2999, "quantity": 1}]'::jsonb  -- line_items
);

-- Verify trial status was converted
SELECT 'After Successful Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

ROLLBACK;

-- Test 2: Create account with expired trial and test successful order conversion
BEGIN;

-- Create test account with expired trial
INSERT INTO public.accounts (id, name, slug, trial_status, trial_started_at, trial_ends_at)
VALUES (
  '123e4567-e89b-12d3-a456-************',
  'Test Account Expired Trial',
  'test-account-expired',
  'expired',
  NOW() - INTERVAL '10 days',
  NOW() - INTERVAL '3 days'
)
ON CONFLICT (id) DO UPDATE SET
  trial_status = 'expired',
  trial_started_at = NOW() - INTERVAL '10 days',
  trial_ends_at = NOW() - INTERVAL '3 days';

-- Verify account has expired trial
SELECT 'Before Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

-- Create successful order using upsert_order function
SELECT public.upsert_order(
  '123e4567-e89b-12d3-a456-************'::uuid,  -- target_account_id
  'test_customer_124',                             -- target_customer_id  
  'order_124',                                     -- target_order_id
  'succeeded'::public.payment_status,              -- status
  'stripe'::public.billing_provider,               -- billing_provider
  2999,                                            -- total_amount (in cents)
  'USD',                                           -- currency
  '[{"id": "item_2", "product_id": "prod_124", "variant_id": "var_124", "price_amount": 2999, "quantity": 1}]'::jsonb  -- line_items
);

-- Verify trial status was converted
SELECT 'After Successful Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

ROLLBACK;

-- Test 3: Create account with inactive trial and verify no change
BEGIN;

-- Create test account with inactive trial
INSERT INTO public.accounts (id, name, slug, trial_status)
VALUES (
  '123e4567-e89b-12d3-a456-************',
  'Test Account Inactive Trial',
  'test-account-inactive',
  'inactive'
)
ON CONFLICT (id) DO UPDATE SET
  trial_status = 'inactive';

-- Verify account has inactive trial
SELECT 'Before Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

-- Create successful order using upsert_order function
SELECT public.upsert_order(
  '123e4567-e89b-12d3-a456-************'::uuid,  -- target_account_id
  'test_customer_125',                             -- target_customer_id  
  'order_125',                                     -- target_order_id
  'succeeded'::public.payment_status,              -- status
  'stripe'::public.billing_provider,               -- billing_provider
  2999,                                            -- total_amount (in cents)
  'USD',                                           -- currency
  '[{"id": "item_3", "product_id": "prod_125", "variant_id": "var_125", "price_amount": 2999, "quantity": 1}]'::jsonb  -- line_items
);

-- Verify trial status remained inactive (no change)
SELECT 'After Successful Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

ROLLBACK;

-- Test 4: Test pending order doesn't convert trial
BEGIN;

-- Create test account with active trial
INSERT INTO public.accounts (id, name, slug, trial_status, trial_started_at, trial_ends_at)
VALUES (
  '123e4567-e89b-12d3-a456-************',
  'Test Account Pending Order',
  'test-account-pending',
  'active',
  NOW(),
  NOW() + INTERVAL '7 days'
)
ON CONFLICT (id) DO UPDATE SET
  trial_status = 'active',
  trial_started_at = NOW(),
  trial_ends_at = NOW() + INTERVAL '7 days';

-- Verify account has active trial
SELECT 'Before Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

-- Create pending order using upsert_order function
SELECT public.upsert_order(
  '123e4567-e89b-12d3-a456-************'::uuid,  -- target_account_id
  'test_customer_126',                             -- target_customer_id  
  'order_126',                                     -- target_order_id
  'pending'::public.payment_status,                -- status (pending, not succeeded)
  'stripe'::public.billing_provider,               -- billing_provider
  2999,                                            -- total_amount (in cents)
  'USD',                                           -- currency
  '[{"id": "item_4", "product_id": "prod_126", "variant_id": "var_126", "price_amount": 2999, "quantity": 1}]'::jsonb  -- line_items
);

-- Verify trial status remained active (no change for pending orders)
SELECT 'After Pending Order:' as test_phase, id, name, trial_status FROM public.accounts WHERE id = '123e4567-e89b-12d3-a456-************';

ROLLBACK;
