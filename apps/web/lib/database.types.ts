export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      accounts: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          trial_ends_at: string | null
          trial_started_at: string | null
          trial_status: "inactive" | "active" | "expired" | "converted"
          updated_at: string | null
          updated_by: string | null
          website: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name: string
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          trial_ends_at?: string | null
          trial_started_at?: string | null
          trial_status?: "inactive" | "active" | "expired" | "converted"
          updated_at?: string | null
          updated_by?: string | null
          website?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name?: string
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          trial_ends_at?: string | null
          trial_started_at?: string | null
          trial_status?: "inactive" | "active" | "expired" | "converted"
          updated_at?: string | null
          updated_by?: string | null
          website?: string | null
        }
        Relationships: []
      }
      accounts_memberships: {
        Row: {
          account_id: string
          account_role: string
          created_at: string
          created_by: string | null
          updated_at: string
          updated_by: string | null
          user_id: string
        }
        Insert: {
          account_id: string
          account_role: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id: string
        }
        Update: {
          account_id?: string
          account_role?: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["account_role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      billing_customers: {
        Row: {
          account_id: string
          customer_id: string
          email: string | null
          id: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Insert: {
          account_id: string
          customer_id: string
          email?: string | null
          id?: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Update: {
          account_id?: string
          customer_id?: string
          email?: string | null
          id?: number
          provider?: Database["public"]["Enums"]["billing_provider"]
        }
        Relationships: [
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_ideas: {
        Row: {
          brief: Json | null
          brief_blocks: Json | null
          campaign_id: string
          channels: Json | null
          company_id: string
          content: string
          content_blocks: Json | null
          content_types: Json | null
          created_at: string
          id: string
          is_selected: boolean | null
          languages: Json | null
          metadata: Json | null
          title: string | null
        }
        Insert: {
          brief?: Json | null
          brief_blocks?: Json | null
          campaign_id: string
          channels?: Json | null
          company_id: string
          content: string
          content_blocks?: Json | null
          content_types?: Json | null
          created_at?: string
          id?: string
          is_selected?: boolean | null
          languages?: Json | null
          metadata?: Json | null
          title?: string | null
        }
        Update: {
          brief?: Json | null
          brief_blocks?: Json | null
          campaign_id?: string
          channels?: Json | null
          company_id?: string
          content?: string
          content_blocks?: Json | null
          content_types?: Json | null
          created_at?: string
          id?: string
          is_selected?: boolean | null
          languages?: Json | null
          metadata?: Json | null
          title?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_ideas_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_ideas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_ideas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_ideas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_personas: {
        Row: {
          campaign_id: string
          created_at: string | null
          id: string
          is_primary: boolean | null
          persona_id: string
        }
        Insert: {
          campaign_id: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id: string
        }
        Update: {
          campaign_id?: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaign_personas_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_personas_persona_id_fkey"
            columns: ["persona_id"]
            isOneToOne: false
            referencedRelation: "personas"
            referencedColumns: ["id"]
          },
        ]
      }
      company_brand: {
        Row: {
          audience: string | null
          brand_colors: Json | null
          brand_fonts: Json | null
          brand_name: string | null
          company_id: string
          created_at: string
          guidelines: string | null
          has_brand_setup: boolean | null
          id: string
          identity: string | null
          is_draft: boolean | null
          messaging_pillars: string | null
          mission: string | null
          personality: string | null
          product_list: string | null
          updated_at: string | null
          value_proposition: string | null
          vision: string | null
          voice: string | null
        }
        Insert: {
          audience?: string | null
          brand_colors?: Json | null
          brand_fonts?: Json | null
          brand_name?: string | null
          company_id: string
          created_at?: string
          guidelines?: string | null
          has_brand_setup?: boolean | null
          id?: string
          identity?: string | null
          is_draft?: boolean | null
          messaging_pillars?: string | null
          mission?: string | null
          personality?: string | null
          product_list?: string | null
          updated_at?: string | null
          value_proposition?: string | null
          vision?: string | null
          voice?: string | null
        }
        Update: {
          audience?: string | null
          brand_colors?: Json | null
          brand_fonts?: Json | null
          brand_name?: string | null
          company_id?: string
          created_at?: string
          guidelines?: string | null
          has_brand_setup?: boolean | null
          id?: string
          identity?: string | null
          is_draft?: boolean | null
          messaging_pillars?: string | null
          mission?: string | null
          personality?: string | null
          product_list?: string | null
          updated_at?: string | null
          value_proposition?: string | null
          vision?: string | null
          voice?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "company_brand_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_brand_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_brand_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      company_campaigns: {
        Row: {
          company_id: string
          created_at: string
          documents: Json | null
          end_date: string | null
          guidelines: string | null
          has_reached_summary: boolean | null
          id: string
          identity: string | null
          kpis: string | null
          messaging: string | null
          metadata: Json | null
          name: string
          objective: string | null
          objectives: string | null
          personality: string | null
          personas: string | null
          slug: string
          start_date: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          targetAudience: string | null
          tone: string | null
          user_id: string
          value_prop: string | null
          visualStyle: string | null
          voice: string | null
        }
        Insert: {
          company_id: string
          created_at?: string
          documents?: Json | null
          end_date?: string | null
          guidelines?: string | null
          has_reached_summary?: boolean | null
          id?: string
          identity?: string | null
          kpis?: string | null
          messaging?: string | null
          metadata?: Json | null
          name: string
          objective?: string | null
          objectives?: string | null
          personality?: string | null
          personas?: string | null
          slug: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          targetAudience?: string | null
          tone?: string | null
          user_id: string
          value_prop?: string | null
          visualStyle?: string | null
          voice?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string
          documents?: Json | null
          end_date?: string | null
          guidelines?: string | null
          has_reached_summary?: boolean | null
          id?: string
          identity?: string | null
          kpis?: string | null
          messaging?: string | null
          metadata?: Json | null
          name?: string
          objective?: string | null
          objectives?: string | null
          personality?: string | null
          personas?: string | null
          slug?: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          targetAudience?: string | null
          tone?: string | null
          user_id?: string
          value_prop?: string | null
          visualStyle?: string | null
          voice?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "company_campaigns_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_campaigns_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_campaigns_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      company_content: {
        Row: {
          avatar_presenter_id: string | null
          avatar_script: string | null
          avatar_video_id: string | null
          avatar_video_url: string | null
          avatar_voice_id: string | null
          campaign_id: string | null
          channel: string | null
          company_id: string | null
          content: string | null
          content_editor_template: Json | null
          content_template: string | null
          content_type: string | null
          created_at: string | null
          has_avatar: boolean | null
          has_image: boolean | null
          has_video_presentation: boolean | null
          id: string
          idea_id: string | null
          image_path: string | null
          image_url: string | null
          is_avatar_ready: boolean | null
          is_draft: boolean | null
          is_generating: boolean | null
          is_posted: boolean | null
          is_scheduled: boolean | null
          language: string | null
          scheduled_publishing_time: string | null
          seo_keywords: Json | null
          status: string | null
          task_description: string | null
          task_id: string | null
          task_title: string | null
          trend_keywords: Json | null
          updated_at: string | null
          video_editor_aspect_ratio: Json | null
          video_editor_overlays: Json | null
          video_editor_player_dimensions: Json | null
          video_presentation_render_params: Json | null
          video_presentation_script: string | null
          video_presentation_url: string | null
          visual_description: string | null
          visual_description_group: Json | null
        }
        Insert: {
          avatar_presenter_id?: string | null
          avatar_script?: string | null
          avatar_video_id?: string | null
          avatar_video_url?: string | null
          avatar_voice_id?: string | null
          campaign_id?: string | null
          channel?: string | null
          company_id?: string | null
          content?: string | null
          content_editor_template?: Json | null
          content_template?: string | null
          content_type?: string | null
          created_at?: string | null
          has_avatar?: boolean | null
          has_image?: boolean | null
          has_video_presentation?: boolean | null
          id?: string
          idea_id?: string | null
          image_path?: string | null
          image_url?: string | null
          is_avatar_ready?: boolean | null
          is_draft?: boolean | null
          is_generating?: boolean | null
          is_posted?: boolean | null
          is_scheduled?: boolean | null
          language?: string | null
          scheduled_publishing_time?: string | null
          seo_keywords?: Json | null
          status?: string | null
          task_description?: string | null
          task_id?: string | null
          task_title?: string | null
          trend_keywords?: Json | null
          updated_at?: string | null
          video_editor_aspect_ratio?: Json | null
          video_editor_overlays?: Json | null
          video_editor_player_dimensions?: Json | null
          video_presentation_render_params?: Json | null
          video_presentation_script?: string | null
          video_presentation_url?: string | null
          visual_description?: string | null
          visual_description_group?: Json | null
        }
        Update: {
          avatar_presenter_id?: string | null
          avatar_script?: string | null
          avatar_video_id?: string | null
          avatar_video_url?: string | null
          avatar_voice_id?: string | null
          campaign_id?: string | null
          channel?: string | null
          company_id?: string | null
          content?: string | null
          content_editor_template?: Json | null
          content_template?: string | null
          content_type?: string | null
          created_at?: string | null
          has_avatar?: boolean | null
          has_image?: boolean | null
          has_video_presentation?: boolean | null
          id?: string
          idea_id?: string | null
          image_path?: string | null
          image_url?: string | null
          is_avatar_ready?: boolean | null
          is_draft?: boolean | null
          is_generating?: boolean | null
          is_posted?: boolean | null
          is_scheduled?: boolean | null
          language?: string | null
          scheduled_publishing_time?: string | null
          seo_keywords?: Json | null
          status?: string | null
          task_description?: string | null
          task_id?: string | null
          task_title?: string | null
          trend_keywords?: Json | null
          updated_at?: string | null
          video_editor_aspect_ratio?: Json | null
          video_editor_overlays?: Json | null
          video_editor_player_dimensions?: Json | null
          video_presentation_render_params?: Json | null
          video_presentation_script?: string | null
          video_presentation_url?: string | null
          visual_description?: string | null
          visual_description_group?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "company_content_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_idea_id_fkey"
            columns: ["idea_id"]
            isOneToOne: false
            referencedRelation: "campaign_ideas"
            referencedColumns: ["id"]
          },
        ]
      }
      company_task_statuses: {
        Row: {
          color: string | null
          company_id: string
          created_at: string | null
          display_name: string
          icon: string | null
          id: string
          name: string
          status_order: number
        }
        Insert: {
          color?: string | null
          company_id: string
          created_at?: string | null
          display_name: string
          icon?: string | null
          id?: string
          name: string
          status_order: number
        }
        Update: {
          color?: string | null
          company_id?: string
          created_at?: string | null
          display_name?: string
          icon?: string | null
          id?: string
          name?: string
          status_order?: number
        }
        Relationships: [
          {
            foreignKeyName: "company_task_statuses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_task_statuses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_task_statuses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      config: {
        Row: {
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing: boolean
          enable_team_account_billing: boolean
          enable_team_accounts: boolean
        }
        Insert: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
        }
        Update: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
        }
        Relationships: []
      }
      content_personas: {
        Row: {
          content_id: string
          created_at: string | null
          id: string
          is_primary: boolean | null
          persona_id: string
        }
        Insert: {
          content_id: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id: string
        }
        Update: {
          content_id?: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_personas_content_id_fkey"
            columns: ["content_id"]
            isOneToOne: false
            referencedRelation: "company_content"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "content_personas_persona_id_fkey"
            columns: ["persona_id"]
            isOneToOne: false
            referencedRelation: "personas"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
        Insert: {
          account_id: string
          created_at?: string
          email: string
          expires_at?: string
          id?: number
          invite_token: string
          invited_by: string
          role: string
          updated_at?: string
        }
        Update: {
          account_id?: string
          created_at?: string
          email?: string
          expires_at?: string
          id?: number
          invite_token?: string
          invited_by?: string
          role?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      linkedInState: {
        Row: {
          access_token: string | null
          created_at: string
          expires_in: string | null
          first_name: string | null
          headline: string | null
          id: number
          last_name: string | null
          person_urn: string | null
          profile_picture_url: string | null
          refresh_token: string | null
          refresh_token_expires_in: string | null
          scope: string | null
          state: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          access_token?: string | null
          created_at?: string
          expires_in?: string | null
          first_name?: string | null
          headline?: string | null
          id?: number
          last_name?: string | null
          person_urn?: string | null
          profile_picture_url?: string | null
          refresh_token?: string | null
          refresh_token_expires_in?: string | null
          scope?: string | null
          state?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          access_token?: string | null
          created_at?: string
          expires_in?: string | null
          first_name?: string | null
          headline?: string | null
          id?: number
          last_name?: string | null
          person_urn?: string | null
          profile_picture_url?: string | null
          refresh_token?: string | null
          refresh_token_expires_in?: string | null
          scope?: string | null
          state?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      notifications: {
        Row: {
          account_id: string
          body: string
          channel: Database["public"]["Enums"]["notification_channel"]
          created_at: string
          dismissed: boolean
          expires_at: string | null
          id: number
          link: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          account_id: string
          body: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          account_id?: string
          body?: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string
          id: string
          order_id: string
          price_amount: number | null
          product_id: string
          quantity: number
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          order_id: string
          price_amount?: number | null
          product_id: string
          quantity?: number
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          order_id?: string
          price_amount?: number | null
          product_id?: string
          quantity?: number
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
        Insert: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at?: string
        }
        Update: {
          account_id?: string
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency?: string
          id?: string
          status?: Database["public"]["Enums"]["payment_status"]
          total_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      personas: {
        Row: {
          avatar_url: string | null
          budget_range: string | null
          buying_stage: string | null
          challenges: Json | null
          channels: Json | null
          communication_style: string | null
          company_id: string
          company_size: string | null
          content_count: number | null
          content_formats: Json | null
          content_length: string | null
          created_at: string | null
          decision_authority: string | null
          department: string | null
          goals: Json | null
          id: string
          industries: Json | null
          info_preferences: Json | null
          last_used: string | null
          location: string | null
          management_level: string | null
          name: string
          role: string
          status: string | null
          tech_stack: Json | null
          topics: Json | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          budget_range?: string | null
          buying_stage?: string | null
          challenges?: Json | null
          channels?: Json | null
          communication_style?: string | null
          company_id: string
          company_size?: string | null
          content_count?: number | null
          content_formats?: Json | null
          content_length?: string | null
          created_at?: string | null
          decision_authority?: string | null
          department?: string | null
          goals?: Json | null
          id?: string
          industries?: Json | null
          info_preferences?: Json | null
          last_used?: string | null
          location?: string | null
          management_level?: string | null
          name: string
          role: string
          status?: string | null
          tech_stack?: Json | null
          topics?: Json | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          budget_range?: string | null
          buying_stage?: string | null
          challenges?: Json | null
          channels?: Json | null
          communication_style?: string | null
          company_id?: string
          company_size?: string | null
          content_count?: number | null
          content_formats?: Json | null
          content_length?: string | null
          created_at?: string | null
          decision_authority?: string | null
          department?: string | null
          goals?: Json | null
          id?: string
          industries?: Json | null
          info_preferences?: Json | null
          last_used?: string | null
          location?: string | null
          management_level?: string | null
          name?: string
          role?: string
          status?: string | null
          tech_stack?: Json | null
          topics?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "personas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "personas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "personas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      product_documents: {
        Row: {
          company_id: string
          content: string | null
          created_at: string
          file_path: string
          file_type: string
          id: string
          title: string
        }
        Insert: {
          company_id: string
          content?: string | null
          created_at?: string
          file_path: string
          file_type: string
          id?: string
          title: string
        }
        Update: {
          company_id?: string
          content?: string | null
          created_at?: string
          file_path?: string
          file_type?: string
          id?: string
          title?: string
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          id: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Insert: {
          id?: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Update: {
          id?: number
          permission?: Database["public"]["Enums"]["app_permissions"]
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      roles: {
        Row: {
          hierarchy_level: number
          name: string
        }
        Insert: {
          hierarchy_level: number
          name: string
        }
        Update: {
          hierarchy_level?: number
          name?: string
        }
        Relationships: []
      }
      subscription_items: {
        Row: {
          created_at: string
          id: string
          interval: string
          interval_count: number
          price_amount: number | null
          product_id: string
          quantity: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          interval: string
          interval_count: number
          price_amount?: number | null
          product_id: string
          quantity?: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          interval?: string
          interval_count?: number
          price_amount?: number | null
          product_id?: string
          quantity?: number
          subscription_id?: string
          type?: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_items_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
        Insert: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at?: string
          currency: string
          id: string
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Update: {
          account_id?: string
          active?: boolean
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end?: boolean
          created_at?: string
          currency?: string
          id?: string
          period_ends_at?: string
          period_starts_at?: string
          status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      twitterState: {
        Row: {
          access_token: string | null
          code_verifier: string | null
          created_at: string
          description: string | null
          expires_in: string | null
          id: number
          name: string | null
          profile_image_url: string | null
          refresh_token: string | null
          refresh_token_expires_in: string | null
          scope: string | null
          screen_name: string | null
          state: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          access_token?: string | null
          code_verifier?: string | null
          created_at?: string
          description?: string | null
          expires_in?: string | null
          id?: number
          name?: string | null
          profile_image_url?: string | null
          refresh_token?: string | null
          refresh_token_expires_in?: string | null
          scope?: string | null
          screen_name?: string | null
          state?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Update: {
          access_token?: string | null
          code_verifier?: string | null
          created_at?: string
          description?: string | null
          expires_in?: string | null
          id?: number
          name?: string | null
          profile_image_url?: string | null
          refresh_token?: string | null
          refresh_token_expires_in?: string | null
          scope?: string | null
          screen_name?: string | null
          state?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      user_account_workspace: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          subscription_status:
            | Database["public"]["Enums"]["subscription_status"]
            | null
        }
        Relationships: []
      }
      user_accounts: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          role: string | null
          slug: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
    }
    Functions: {
      accept_invitation: {
        Args: { token: string; user_id: string }
        Returns: string
      }
      add_company_task_status: {
        Args: {
          p_company_id: string
          p_name: string
          p_display_name: string
          p_color?: string
          p_icon?: string
        }
        Returns: string
      }
      add_invitations_to_account: {
        Args: {
          account_slug: string
          invitations: Database["public"]["CompositeTypes"]["invitation"][]
        }
        Returns: Database["public"]["Tables"]["invitations"]["Row"][]
      }
      can_action_account_member: {
        Args: { target_team_account_id: string; target_user_id: string }
        Returns: boolean
      }
      create_invitation: {
        Args: { account_id: string; email: string; role: string }
        Returns: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
      }
      create_team_account: {
        Args: { account_name: string, website: string, public_data: string }
        Returns: {
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
        }
      }
      delete_company_task_status: {
        Args: {
          p_company_id: string
          p_status_id: string
          p_replacement_status?: string
        }
        Returns: boolean
      }
      get_account_invitations: {
        Args: { account_slug: string }
        Returns: {
          id: number
          email: string
          account_id: string
          invited_by: string
          role: string
          created_at: string
          updated_at: string
          expires_at: string
          inviter_name: string
          inviter_email: string
        }[]
      }
      get_account_members: {
        Args: { account_slug: string }
        Returns: {
          id: string
          user_id: string
          account_id: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          name: string
          email: string
          picture_url: string
          created_at: string
          updated_at: string
        }[]
      }
      get_company_task_statuses: {
        Args: { p_company_id: string }
        Returns: {
          id: string
          name: string
          display_name: string
          status_order: number
          color: string
          icon: string
        }[]
      }
      get_config: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_upper_system_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      has_active_subscription: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      has_more_elevated_role: {
        Args: {
          target_user_id: string
          target_account_id: string
          role_name: string
        }
        Returns: boolean
      }
      has_permission: {
        Args: {
          user_id: string
          account_id: string
          permission_name: Database["public"]["Enums"]["app_permissions"]
        }
        Returns: boolean
      }
      has_role_on_account: {
        Args: { account_id: string; account_role?: string }
        Returns: boolean
      }
      has_same_role_hierarchy_level: {
        Args: {
          target_user_id: string
          target_account_id: string
          role_name: string
        }
        Returns: boolean
      }
      initialize_company_task_statuses: {
        Args: { p_company_id: string }
        Returns: undefined
      }
      is_account_owner: {
        Args: { account_id: string }
        Returns: boolean
      }
      is_account_team_member: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      is_set: {
        Args: { field_name: string }
        Returns: boolean
      }
      is_team_member: {
        Args: { account_id: string; user_id: string }
        Returns: boolean
      }
      is_valid_task_status: {
        Args: { p_company_id: string; p_status: string }
        Returns: boolean
      }
      reorder_company_task_statuses: {
        Args: { p_company_id: string; p_status_ids: string[] }
        Returns: boolean
      }
      team_account_workspace: {
        Args: { account_slug: string }
        Returns: {
          id: string
          name: string
          picture_url: string
          slug: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          subscription_status: Database["public"]["Enums"]["subscription_status"]
          permissions: Database["public"]["Enums"]["app_permissions"][]
        }[]
      }
      transfer_team_account_ownership: {
        Args: { target_account_id: string; new_owner_id: string }
        Returns: undefined
      }
      update_company_task_status: {
        Args: {
          p_status_id: string
          p_display_name?: string
          p_status_order?: number
          p_color?: string
          p_icon?: string
        }
        Returns: boolean
      }
      upsert_order: {
        Args: {
          target_account_id: string
          target_customer_id: string
          target_order_id: string
          status: Database["public"]["Enums"]["payment_status"]
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          total_amount: number
          currency: string
          line_items: Json
        }
        Returns: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
      }
      upsert_subscription: {
        Args: {
          target_account_id: string
          target_customer_id: string
          target_subscription_id: string
          active: boolean
          status: Database["public"]["Enums"]["subscription_status"]
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          currency: string
          period_starts_at: string
          period_ends_at: string
          line_items: Json
          trial_starts_at?: string
          trial_ends_at?: string
        }
        Returns: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
      }
    }
    Enums: {
      app_permissions:
        | "roles.manage"
        | "billing.manage"
        | "settings.manage"
        | "members.manage"
        | "invites.manage"
      billing_provider: "stripe" | "lemon-squeezy" | "paddle"
      campaign_status:
        | "Draft"
        | "Ready"
        | "In Progress"
        | "Completed"
        | "Archived"
      notification_channel: "in_app" | "email"
      notification_type: "info" | "warning" | "error"
      payment_status: "pending" | "succeeded" | "failed"
      subscription_item_type: "flat" | "per_seat" | "metered"
      subscription_status:
        | "active"
        | "trialing"
        | "past_due"
        | "canceled"
        | "unpaid"
        | "incomplete"
        | "incomplete_expired"
        | "paused"
    }
    CompositeTypes: {
      invitation: {
        email: string | null
        role: string | null
      }
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          level: number | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      prefixes: {
        Row: {
          bucket_id: string
          created_at: string | null
          level: number
          name: string
          updated_at: string | null
        }
        Insert: {
          bucket_id: string
          created_at?: string | null
          level?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          bucket_id?: string
          created_at?: string | null
          level?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prefixes_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_prefixes: {
        Args: { _bucket_id: string; _name: string }
        Returns: undefined
      }
      can_insert_object: {
        Args: { bucketid: string; name: string; owner: string; metadata: Json }
        Returns: undefined
      }
      delete_prefix: {
        Args: { _bucket_id: string; _name: string }
        Returns: boolean
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_level: {
        Args: { name: string }
        Returns: number
      }
      get_prefix: {
        Args: { name: string }
        Returns: string
      }
      get_prefixes: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_legacy_v1: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v1_optimised: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v2: {
        Args: {
          prefix: string
          bucket_name: string
          limits?: number
          levels?: number
          start_after?: string
        }
        Returns: {
          key: string
          name: string
          id: string
          updated_at: string
          created_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      app_permissions: [
        "roles.manage",
        "billing.manage",
        "settings.manage",
        "members.manage",
        "invites.manage",
      ],
      billing_provider: ["stripe", "lemon-squeezy", "paddle"],
      campaign_status: [
        "Draft",
        "Ready",
        "In Progress",
        "Completed",
        "Archived",
      ],
      notification_channel: ["in_app", "email"],
      notification_type: ["info", "warning", "error"],
      payment_status: ["pending", "succeeded", "failed"],
      subscription_item_type: ["flat", "per_seat", "metered"],
      subscription_status: [
        "active",
        "trialing",
        "past_due",
        "canceled",
        "unpaid",
        "incomplete",
        "incomplete_expired",
        "paused",
      ],
    },
  },
  storage: {
    Enums: {},
  },
} as const

