-- Migration: Move Trial Logic from Accounts to Subscriptions
-- This migration removes trial fields from accounts and creates subscription-based trial logic

-- First, let's create helper functions to determine trial status based on subscriptions
CREATE OR REPLACE FUNCTION public.get_account_trial_status(account_id uuid)
RETURNS public.trial_status_enum AS $$
DECLARE
    trial_status public.trial_status_enum;
    active_subscription record;
BEGIN
    -- Get the most recent active subscription for this account
    SELECT * INTO active_subscription
    FROM public.subscriptions
    WHERE subscriptions.account_id = get_account_trial_status.account_id
    AND subscriptions.active = true
    ORDER BY created_at DESC
    LIMIT 1;

    -- If no active subscription, check for any subscription with trial
    IF active_subscription IS NULL THEN
        SELECT * INTO active_subscription
        FROM public.subscriptions
        WHERE subscriptions.account_id = get_account_trial_status.account_id
        AND subscriptions.trial_starts_at IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 1;
    END IF;

    -- Determine trial status based on subscription
    IF active_subscription IS NULL THEN
        -- No subscription at all
        trial_status := 'inactive';
    ELSIF active_subscription.status = 'trialing' THEN
        -- Currently in trial
        IF active_subscription.trial_ends_at IS NOT NULL AND active_subscription.trial_ends_at < NOW() THEN
            trial_status := 'expired';
        ELSE
            trial_status := 'active';
        END IF;
    ELSIF active_subscription.status = 'active' AND active_subscription.trial_starts_at IS NOT NULL THEN
        -- Was in trial, now converted to paid
        trial_status := 'converted';
    ELSIF active_subscription.status = 'active' THEN
        -- Active paid subscription (no trial)
        trial_status := 'converted';
    ELSE
        -- Other statuses (canceled, past_due, etc.)
        IF active_subscription.trial_ends_at IS NOT NULL AND active_subscription.trial_ends_at < NOW() THEN
            trial_status := 'expired';
        ELSE
            trial_status := 'inactive';
        END IF;
    END IF;

    RETURN trial_status;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trial start date from subscriptions
CREATE OR REPLACE FUNCTION public.get_account_trial_started_at(account_id uuid)
RETURNS timestamptz AS $$
DECLARE
    trial_start timestamptz;
BEGIN
    SELECT trial_starts_at INTO trial_start
    FROM public.subscriptions
    WHERE subscriptions.account_id = get_account_trial_started_at.account_id
    AND subscriptions.trial_starts_at IS NOT NULL
    ORDER BY created_at DESC
    LIMIT 1;

    RETURN trial_start;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trial end date from subscriptions
CREATE OR REPLACE FUNCTION public.get_account_trial_ends_at(account_id uuid)
RETURNS timestamptz AS $$
DECLARE
    trial_end timestamptz;
BEGIN
    SELECT trial_ends_at INTO trial_end
    FROM public.subscriptions
    WHERE subscriptions.account_id = get_account_trial_ends_at.account_id
    AND subscriptions.trial_ends_at IS NOT NULL
    ORDER BY created_at DESC
    LIMIT 1;

    RETURN trial_end;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if account is in trial
CREATE OR REPLACE FUNCTION public.is_account_in_trial(account_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN public.get_account_trial_status(account_id) = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if account trial has expired
CREATE OR REPLACE FUNCTION public.is_account_trial_expired(account_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN public.get_account_trial_status(account_id) = 'expired';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if account is converted (paid)
CREATE OR REPLACE FUNCTION public.is_account_converted(account_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN public.get_account_trial_status(account_id) = 'converted';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION public.get_account_trial_status(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_account_trial_started_at(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_account_trial_ends_at(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_account_in_trial(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_account_trial_expired(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_account_converted(uuid) TO authenticated;

GRANT EXECUTE ON FUNCTION public.get_account_trial_status(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_account_trial_started_at(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_account_trial_ends_at(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.is_account_in_trial(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.is_account_trial_expired(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION public.is_account_converted(uuid) TO service_role;

-- Update upsert_subscription function to remove trial status conversion logic
-- (since trial status is now determined by subscription status)
CREATE OR REPLACE FUNCTION public.upsert_subscription (
  target_account_id uuid,
  target_customer_id varchar(255),
  target_subscription_id text,
  active bool,
  status public.subscription_status,
  billing_provider public.billing_provider,
  cancel_at_period_end bool,
  currency varchar(3),
  period_starts_at timestamptz,
  period_ends_at timestamptz,
  line_items jsonb,
  trial_starts_at timestamptz default null,
  trial_ends_at timestamptz default null
) returns public.subscriptions
set
  search_path = '' as $$
declare
    new_subscription public.subscriptions;
    new_billing_customer_id int;
begin
    insert into public.billing_customers(
        account_id,
        provider,
        customer_id)
    values (
        target_account_id,
        billing_provider,
        target_customer_id)
on conflict (
    account_id,
    provider,
    customer_id)
    do update set
        provider = excluded.provider
    returning
        id into new_billing_customer_id;

    insert into public.subscriptions(
        account_id,
        billing_customer_id,
        id,
        active,
        status,
        billing_provider,
        cancel_at_period_end,
        currency,
        period_starts_at,
        period_ends_at,
        trial_starts_at,
        trial_ends_at)
    values (
        target_account_id,
        new_billing_customer_id,
        target_subscription_id,
        active,
        status,
        billing_provider,
        cancel_at_period_end,
        currency,
        period_starts_at,
        period_ends_at,
        trial_starts_at,
        trial_ends_at)
on conflict (
    id)
    do update set
        active = excluded.active,
        status = excluded.status,
        cancel_at_period_end = excluded.cancel_at_period_end,
        currency = excluded.currency,
        period_starts_at = excluded.period_starts_at,
        period_ends_at = excluded.period_ends_at,
        trial_starts_at = excluded.trial_starts_at,
        trial_ends_at = excluded.trial_ends_at
    returning
        * into new_subscription;

    -- Note: Trial status is now determined by subscription status and trial dates
    -- No need to update accounts table trial_status field

    -- Upsert subscription items and delete ones that are not in the line_items array
    with item_data as (
        select
            (line_item ->> 'id')::varchar as line_item_id,
            (line_item ->> 'product_id')::varchar as prod_id,
            (line_item ->> 'variant_id')::varchar as var_id,
            (line_item ->> 'type')::public.subscription_item_type as type,
            (line_item ->> 'price_amount')::numeric as price_amt,
            (line_item ->> 'quantity')::integer as qty,
            (line_item ->> 'interval')::varchar as intv,
            (line_item ->> 'interval_count')::integer as intv_count
        from
            jsonb_array_elements(line_items) as line_item
    ),
    line_item_ids as (
        select line_item_id from item_data
    ),
    deleted_items as (
        delete from
            public.subscription_items
        where
            public.subscription_items.subscription_id = new_subscription.id
            and public.subscription_items.id not in (select line_item_id from line_item_ids)
        returning *
    )
    insert into public.subscription_items(
        id,
        subscription_id,
        product_id,
        variant_id,
        type,
        price_amount,
        quantity,
        interval,
        interval_count)
    select
        line_item_id,
        target_subscription_id,
        prod_id,
        var_id,
        type,
        price_amt,
        qty,
        intv,
        intv_count
    from
        item_data
    on conflict (id)
        do update set
            product_id = excluded.product_id,
            variant_id = excluded.variant_id,
            price_amount = excluded.price_amount,
            quantity = excluded.quantity,
            interval = excluded.interval,
            type = excluded.type,
            interval_count = excluded.interval_count;

    return new_subscription;

end;

$$ language plpgsql;

-- Update upsert_order function to remove trial status conversion logic
CREATE OR REPLACE FUNCTION public.upsert_order (
  target_account_id uuid,
  target_customer_id varchar(255),
  target_order_id text,
  status public.payment_status,
  billing_provider public.billing_provider,
  total_amount numeric,
  currency varchar(3),
  line_items jsonb
) returns public.orders
set
  search_path = '' as $$
declare
    new_order public.orders;
    new_billing_customer_id int;
begin
    insert into public.billing_customers(
        account_id,
        provider,
        customer_id)
    values (
        target_account_id,
        billing_provider,
        target_customer_id)
on conflict (
    account_id,
    provider,
    customer_id)
    do update set
        provider = excluded.provider
    returning
        id into new_billing_customer_id;

    insert into public.orders(
        account_id,
        billing_customer_id,
        id,
        status,
        billing_provider,
        total_amount,
        currency)
    values (
        target_account_id,
        new_billing_customer_id,
        target_order_id,
        status,
        billing_provider,
        total_amount,
        currency)
on conflict (
    id)
    do update set
        status = excluded.status,
        total_amount = excluded.total_amount,
        currency = excluded.currency
    returning
        * into new_order;

    -- Upsert order items and delete ones that are not in the line_items array
    with item_data as (
        select
            (line_item ->> 'id')::varchar as line_item_id,
            (line_item ->> 'product_id')::varchar as prod_id,
            (line_item ->> 'variant_id')::varchar as var_id,
            (line_item ->> 'price_amount')::numeric as price_amt,
            (line_item ->> 'quantity')::integer as qty
        from
            jsonb_array_elements(line_items) as line_item
    ),
    line_item_ids as (
        select line_item_id from item_data
    ),
    deleted_items as (
        delete from
            public.order_items
        where
            public.order_items.order_id = new_order.id
            and public.order_items.id not in (select line_item_id from line_item_ids)
        returning *
    )
    insert into public.order_items(
        id,
        order_id,
        product_id,
        variant_id,
        price_amount,
        quantity)
    select
        line_item_id,
        target_order_id,
        prod_id,
        var_id,
        price_amt,
        qty
    from
        item_data
    on conflict (id)
        do update set
            price_amount = excluded.price_amount,
            product_id = excluded.product_id,
            variant_id = excluded.variant_id,
            quantity = excluded.quantity;

    -- Note: Trial status is now determined by subscription status
    -- No need to update accounts table trial_status field

    return new_order;

end;

$$ language plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.upsert_subscription (
  uuid,
  varchar,
  text,
  bool,
  public.subscription_status,
  public.billing_provider,
  bool,
  varchar,
  timestamptz,
  timestamptz,
  jsonb,
  timestamptz,
  timestamptz
) TO service_role;

GRANT EXECUTE ON FUNCTION public.upsert_order (
  uuid,
  varchar,
  text,
  public.payment_status,
  public.billing_provider,
  numeric,
  varchar,
  jsonb
) TO service_role;

-- Add comments to document the new trial logic
COMMENT ON FUNCTION public.upsert_subscription IS 'Upserts a subscription - trial status is now determined by subscription status and trial dates';
COMMENT ON FUNCTION public.upsert_order IS 'Upserts an order - trial status is now determined by subscription status';
COMMENT ON FUNCTION public.get_account_trial_status IS 'Determines trial status based on active subscriptions for an account';
COMMENT ON FUNCTION public.get_account_trial_started_at IS 'Gets trial start date from the most recent subscription with trial';
COMMENT ON FUNCTION public.get_account_trial_ends_at IS 'Gets trial end date from the most recent subscription with trial';
COMMENT ON FUNCTION public.is_account_in_trial IS 'Checks if an account is currently in trial based on subscription status';
COMMENT ON FUNCTION public.is_account_trial_expired IS 'Checks if an account trial has expired based on subscription status';
COMMENT ON FUNCTION public.is_account_converted IS 'Checks if an account is converted to paid based on subscription status'; 