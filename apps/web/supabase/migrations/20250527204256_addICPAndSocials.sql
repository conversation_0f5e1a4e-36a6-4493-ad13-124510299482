create type "public"."lifecycle_stage" as enum ('Startup', 'Scale-up', 'Mature Enterprise');

drop policy "ayrshare_user_profile_delete" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_insert" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_select" on "public"."ayrshare_user_profile";

drop policy "ayrshare_user_profile_update" on "public"."ayrshare_user_profile";

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_company_id_fkey";

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_user_id_fkey";

alter table "public"."ayrshare_user_profile" drop constraint "ayrshare_user_profile_pkey";

drop index if exists "public"."ayrshare_user_profile_company_id_idx";

drop index if exists "public"."ayrshare_user_profile_user_company_idx";

drop index if exists "public"."ayrshare_user_profile_user_id_idx";

drop index if exists "public"."ayrshare_user_profile_pkey";

-- dropt table if exists
drop table if exists "public"."feature_usage";

create table "public"."feature_usage" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "feature" character varying(255) not null,
    "usage" jsonb not null default '{}'::jsonb,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."feature_usage" enable row level security;

create table "public"."icps" (
    "id" uuid not null default gen_random_uuid(),
    "name" character varying(255) not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "company_id" uuid not null,
    "data" jsonb not null default '{}'::jsonb
);


create table "public"."onboarding" (
    "id" uuid not null default uuid_generate_v4(),
    "account_id" uuid not null,
    "data" jsonb default '{}'::jsonb,
    "completed" boolean default false,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."onboarding" enable row level security;

create table "public"."saved_research" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "icp_id" uuid not null,
    "persona_id" uuid not null,
    "title" text not null,
    "description" text not null,
    "source" text not null,
    "relevance_score" integer not null,
    "research_type" text not null,
    "time_filter" text not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now(),
    "source_url" text
);


alter table "public"."saved_research" enable row level security;

alter table "public"."ayrshare_user_profile" alter column "profileKey" set not null;

alter table "public"."ayrshare_user_profile" alter column "refId" set not null;

alter table "public"."ayrshare_user_profile" alter column "user_id" set not null;

alter table "public"."ayrshare_user_profile" disable row level security;

alter table "public"."company_brand" disable row level security;

alter table "public"."personas" add column "icp_id" uuid;

CREATE UNIQUE INDEX feature_usage_account_id_feature_key ON public.feature_usage USING btree (account_id, feature);

CREATE UNIQUE INDEX feature_usage_pkey ON public.feature_usage USING btree (id);

CREATE INDEX icps_company_id_idx ON public.icps USING btree (company_id);

CREATE INDEX icps_data_gin_idx ON public.icps USING gin (data);

CREATE INDEX icps_name_idx ON public.icps USING btree (name);

CREATE UNIQUE INDEX icps_pkey ON public.icps USING btree (id);

CREATE INDEX idx_feature_usage_account_id ON public.feature_usage USING btree (account_id, feature);

CREATE INDEX idx_saved_research_account_id ON public.saved_research USING btree (account_id);

CREATE INDEX idx_saved_research_icp_persona ON public.saved_research USING btree (icp_id, persona_id);

CREATE UNIQUE INDEX onboarding_account_id_key ON public.onboarding USING btree (account_id);

CREATE UNIQUE INDEX onboarding_pkey ON public.onboarding USING btree (id);

CREATE INDEX personas_icp_id_idx ON public.personas USING btree (icp_id);

CREATE UNIQUE INDEX saved_research_account_id_icp_id_persona_id_title_research__key ON public.saved_research USING btree (account_id, icp_id, persona_id, title, research_type);

CREATE UNIQUE INDEX saved_research_pkey ON public.saved_research USING btree (id);

CREATE UNIQUE INDEX ayrshare_user_profile_pkey ON public.ayrshare_user_profile USING btree (id, user_id);

alter table "public"."feature_usage" add constraint "feature_usage_pkey" PRIMARY KEY using index "feature_usage_pkey";

alter table "public"."icps" add constraint "icps_pkey" PRIMARY KEY using index "icps_pkey";

alter table "public"."onboarding" add constraint "onboarding_pkey" PRIMARY KEY using index "onboarding_pkey";

alter table "public"."saved_research" add constraint "saved_research_pkey" PRIMARY KEY using index "saved_research_pkey";

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_pkey" PRIMARY KEY using index "ayrshare_user_profile_pkey";

alter table "public"."feature_usage" add constraint "feature_usage_account_id_feature_key" UNIQUE using index "feature_usage_account_id_feature_key";

alter table "public"."feature_usage" add constraint "feature_usage_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."feature_usage" validate constraint "feature_usage_account_id_fkey";

alter table "public"."icps" add constraint "icps_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."icps" validate constraint "icps_company_id_fkey";

alter table "public"."onboarding" add constraint "onboarding_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) not valid;

alter table "public"."onboarding" validate constraint "onboarding_account_id_fkey";

alter table "public"."onboarding" add constraint "onboarding_account_id_key" UNIQUE using index "onboarding_account_id_key";

alter table "public"."personas" add constraint "personas_icp_id_fkey" FOREIGN KEY (icp_id) REFERENCES icps(id) ON DELETE CASCADE not valid;

alter table "public"."personas" validate constraint "personas_icp_id_fkey";

alter table "public"."saved_research" add constraint "saved_research_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."saved_research" validate constraint "saved_research_account_id_fkey";

alter table "public"."saved_research" add constraint "saved_research_account_id_icp_id_persona_id_title_research__key" UNIQUE using index "saved_research_account_id_icp_id_persona_id_title_research__key";

alter table "public"."saved_research" add constraint "saved_research_relevance_score_check" CHECK (((relevance_score >= 0) AND (relevance_score <= 10))) not valid;

alter table "public"."saved_research" validate constraint "saved_research_relevance_score_check";

alter table "public"."saved_research" add constraint "saved_research_research_type_check" CHECK ((research_type = ANY (ARRAY['pain-points'::text, 'trending-topics'::text, 'recent-news'::text]))) not valid;

alter table "public"."saved_research" validate constraint "saved_research_research_type_check";

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_company_id_fkey" FOREIGN KEY (company_id) REFERENCES accounts(id) not valid;

alter table "public"."ayrshare_user_profile" validate constraint "ayrshare_user_profile_company_id_fkey";

alter table "public"."ayrshare_user_profile" add constraint "ayrshare_user_profile_user_id_fkey" FOREIGN KEY (user_id) REFERENCES accounts(id) not valid;

alter table "public"."ayrshare_user_profile" validate constraint "ayrshare_user_profile_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_feature_usage_row()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.feature_usage (account_id, feature)
  VALUES (NEW.id, '');
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.trigger_set_icps_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$
;

grant delete on table "public"."feature_usage" to "anon";

grant insert on table "public"."feature_usage" to "anon";

grant references on table "public"."feature_usage" to "anon";

grant select on table "public"."feature_usage" to "anon";

grant trigger on table "public"."feature_usage" to "anon";

grant truncate on table "public"."feature_usage" to "anon";

grant update on table "public"."feature_usage" to "anon";

grant delete on table "public"."feature_usage" to "authenticated";

grant insert on table "public"."feature_usage" to "authenticated";

grant references on table "public"."feature_usage" to "authenticated";

grant select on table "public"."feature_usage" to "authenticated";

grant trigger on table "public"."feature_usage" to "authenticated";

grant truncate on table "public"."feature_usage" to "authenticated";

grant update on table "public"."feature_usage" to "authenticated";

grant delete on table "public"."feature_usage" to "service_role";

grant insert on table "public"."feature_usage" to "service_role";

grant references on table "public"."feature_usage" to "service_role";

grant select on table "public"."feature_usage" to "service_role";

grant trigger on table "public"."feature_usage" to "service_role";

grant truncate on table "public"."feature_usage" to "service_role";

grant update on table "public"."feature_usage" to "service_role";

grant delete on table "public"."icps" to "anon";

grant insert on table "public"."icps" to "anon";

grant references on table "public"."icps" to "anon";

grant select on table "public"."icps" to "anon";

grant trigger on table "public"."icps" to "anon";

grant truncate on table "public"."icps" to "anon";

grant update on table "public"."icps" to "anon";

grant delete on table "public"."icps" to "authenticated";

grant insert on table "public"."icps" to "authenticated";

grant references on table "public"."icps" to "authenticated";

grant select on table "public"."icps" to "authenticated";

grant trigger on table "public"."icps" to "authenticated";

grant truncate on table "public"."icps" to "authenticated";

grant update on table "public"."icps" to "authenticated";

grant delete on table "public"."icps" to "service_role";

grant insert on table "public"."icps" to "service_role";

grant references on table "public"."icps" to "service_role";

grant select on table "public"."icps" to "service_role";

grant trigger on table "public"."icps" to "service_role";

grant truncate on table "public"."icps" to "service_role";

grant update on table "public"."icps" to "service_role";

grant delete on table "public"."onboarding" to "anon";

grant insert on table "public"."onboarding" to "anon";

grant references on table "public"."onboarding" to "anon";

grant select on table "public"."onboarding" to "anon";

grant trigger on table "public"."onboarding" to "anon";

grant truncate on table "public"."onboarding" to "anon";

grant update on table "public"."onboarding" to "anon";

grant delete on table "public"."onboarding" to "authenticated";

grant insert on table "public"."onboarding" to "authenticated";

grant references on table "public"."onboarding" to "authenticated";

grant select on table "public"."onboarding" to "authenticated";

grant trigger on table "public"."onboarding" to "authenticated";

grant truncate on table "public"."onboarding" to "authenticated";

grant update on table "public"."onboarding" to "authenticated";

grant delete on table "public"."onboarding" to "service_role";

grant select on table "public"."onboarding" to "service_role";

grant delete on table "public"."saved_research" to "anon";

grant insert on table "public"."saved_research" to "anon";

grant references on table "public"."saved_research" to "anon";

grant select on table "public"."saved_research" to "anon";

grant trigger on table "public"."saved_research" to "anon";

grant truncate on table "public"."saved_research" to "anon";

grant update on table "public"."saved_research" to "anon";

grant delete on table "public"."saved_research" to "authenticated";

grant insert on table "public"."saved_research" to "authenticated";

grant references on table "public"."saved_research" to "authenticated";

grant select on table "public"."saved_research" to "authenticated";

grant trigger on table "public"."saved_research" to "authenticated";

grant truncate on table "public"."saved_research" to "authenticated";

grant update on table "public"."saved_research" to "authenticated";

grant delete on table "public"."saved_research" to "service_role";

grant insert on table "public"."saved_research" to "service_role";

grant references on table "public"."saved_research" to "service_role";

grant select on table "public"."saved_research" to "service_role";

grant trigger on table "public"."saved_research" to "service_role";

grant truncate on table "public"."saved_research" to "service_role";

grant update on table "public"."saved_research" to "service_role";

create policy "select_feature_usage"
on "public"."feature_usage"
as permissive
for select
to authenticated
using ((has_role_on_account(account_id) OR (( SELECT auth.uid() AS uid) = account_id)));


create policy "icps_delete_policy"
on "public"."icps"
as permissive
for delete
to authenticated
using (((company_id = ( SELECT auth.uid() AS uid)) OR has_role_on_account(company_id)));


create policy "icps_insert_policy"
on "public"."icps"
as permissive
for insert
to authenticated
with check (((company_id = ( SELECT auth.uid() AS uid)) OR has_role_on_account(company_id)));


create policy "icps_select_policy"
on "public"."icps"
as permissive
for select
to authenticated
using (((company_id = ( SELECT auth.uid() AS uid)) OR has_role_on_account(company_id)));


create policy "icps_update_policy"
on "public"."icps"
as permissive
for update
to authenticated
using (((company_id = ( SELECT auth.uid() AS uid)) OR has_role_on_account(company_id)))
with check (((company_id = ( SELECT auth.uid() AS uid)) OR has_role_on_account(company_id)));


create policy "insert_onboarding"
on "public"."onboarding"
as permissive
for insert
to authenticated
with check ((account_id = ( SELECT auth.uid() AS uid)));


create policy "read_onboarding"
on "public"."onboarding"
as permissive
for select
to authenticated
using ((account_id = ( SELECT auth.uid() AS uid)));


create policy "update_onboarding"
on "public"."onboarding"
as permissive
for update
to authenticated
using ((account_id = ( SELECT auth.uid() AS uid)))
with check ((account_id = ( SELECT auth.uid() AS uid)));


create policy "saved_research_delete"
on "public"."saved_research"
as permissive
for delete
to authenticated
using (has_role_on_account(account_id));


create policy "saved_research_insert"
on "public"."saved_research"
as permissive
for insert
to authenticated
with check (has_role_on_account(account_id));


create policy "saved_research_read"
on "public"."saved_research"
as permissive
for select
to authenticated
using (has_role_on_account(account_id));


CREATE TRIGGER create_feature_usage_row AFTER INSERT ON public.accounts FOR EACH ROW EXECUTE FUNCTION create_feature_usage_row();

CREATE TRIGGER set_icps_updated_at BEFORE UPDATE ON public.icps FOR EACH ROW EXECUTE FUNCTION trigger_set_icps_updated_at();

CREATE TRIGGER saved_research_set_timestamps BEFORE UPDATE ON public.saved_research FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamps();




