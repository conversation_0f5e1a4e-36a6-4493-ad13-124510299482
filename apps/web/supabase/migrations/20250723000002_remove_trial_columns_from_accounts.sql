-- Migration: Remove Trial Columns from Accounts Table
-- This migration removes trial-related columns from accounts since trial logic is now subscription-based

-- Remove trial-related columns from accounts table
ALTER TABLE public.accounts DROP COLUMN IF EXISTS trial_started_at;
ALTER TABLE public.accounts DROP COLUMN IF EXISTS trial_ends_at;
ALTER TABLE public.accounts DROP COLUMN IF EXISTS trial_status;

-- Remove trial-related indexes
DROP INDEX IF EXISTS idx_accounts_trial_status;
DROP INDEX IF EXISTS idx_accounts_trial_ends_at;

-- Remove trial-related constraints
ALTER TABLE public.accounts DROP CONSTRAINT IF EXISTS check_trial_status_valid;

-- Note: Trial columns have been removed from accounts table
-- Trial logic is now subscription-based using helper functions

-- Update the trial system migration to remove the trial columns creation
-- (This is handled by the previous migration that creates helper functions) 