import { type TrialInfo } from '../hooks/use-trial-status';

export function getTrialDisplayText(
  trialInfo: TrialInfo,
  t: (key: string, options?: any) => string,
  options: {
    showDaysRemaining?: boolean;
    compact?: boolean;
  } = {}
): string | null {
  const { showDaysRemaining = true, compact = false } = options;
  
  // For compact badges, don't show converted or inactive status
  if (compact && (trialInfo.status === 'converted' || trialInfo.status === 'inactive')) {
    return null;
  }

  // Handle different trial statuses
  switch (trialInfo.status) {
    case 'expired':
      return t('status.expired.badge');

    case 'converted':
      return t('status.converted.badge');

    case 'inactive':
      return t('status.inactive.badge');

    case 'active': {
      if (!showDaysRemaining || trialInfo.daysRemaining === null) {
        return t('status.active.badge');
      }

      const days = trialInfo.daysRemaining;

      if (days <= 0) {
        return t('trialEndsToday');
      } else if (days === 1) {
        return t('trialEndsToday');
      } else {
        // Use simple string construction for reliable display
        const dayWord = days === 1 ? t('day') : t('days');
        return `${t('trialEndsIn')} ${days} ${dayWord}`;
      }
    }

    default:
      return t('status.active.badge');
  }
}

export function getTrialActiveDaysRemainingText(
  trialInfo: TrialInfo,
  t: (key: string, options?: any) => string
): string {
  if (trialInfo.status !== 'active' || trialInfo.daysRemaining === null) {
    return '';
  }

  const days = trialInfo.daysRemaining;

  if (days <= 0) {
    return t('trialEndsToday');
  } else if (days === 1) {
    return t('trialEndsToday');
  } else {
    // Use simple string construction for reliable display
    const dayWord = days === 1 ? t('day') : t('days');
    return `${t('trialEndsIn')} ${days} ${dayWord}`;
  }
} 