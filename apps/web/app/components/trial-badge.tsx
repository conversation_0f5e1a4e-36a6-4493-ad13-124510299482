'use client';

import { cva, type VariantProps } from 'class-variance-authority';
import { Clock, Crown, AlertTriangle, CheckCircle } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { cn } from '@kit/ui/utils';
import { useTranslation } from 'react-i18next';
import { type TrialInfo } from '../hooks/use-trial-status';
import { getTrialDisplayText } from '../utils/trial-text';

const trialBadgeVariants = cva(
  'inline-flex items-center gap-1.5 font-medium shadow-none',
  {
    variants: {
      status: {
        active: 'bg-blue-50 hover:bg-blue-50 text-blue-600 border-blue-200 dark:bg-blue-500/10 dark:hover:bg-blue-500/10 dark:text-blue-400 dark:border-blue-500/20',
        expired: 'bg-red-50 hover:bg-red-50 text-red-600 border-red-200 dark:bg-red-500/10 dark:hover:bg-red-500/10 dark:text-red-400 dark:border-red-500/20',
        converted: 'bg-green-50 hover:bg-green-50 text-green-600 border-green-200 dark:bg-green-500/10 dark:hover:bg-green-500/10 dark:text-green-400 dark:border-green-500/20',
        inactive: 'bg-gray-50 hover:bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-500/10 dark:hover:bg-gray-500/10 dark:text-gray-400 dark:border-gray-500/20',
      },
      size: {
        sm: 'text-xs px-2 py-0.5 [&>svg]:size-3',
        md: 'text-sm px-2.5 py-1 [&>svg]:size-3.5',
        lg: 'text-base px-3 py-1.5 [&>svg]:size-4',
      },
    },
    defaultVariants: {
      status: 'active',
      size: 'md',
    },
  }
);

interface TrialBadgeProps extends VariantProps<typeof trialBadgeVariants> {
  trialInfo: TrialInfo;
  showIcon?: boolean;
  showDaysRemaining?: boolean;
  className?: string;
}

const statusIcons = {
  active: Clock,
  expired: AlertTriangle,
  converted: Crown,
  inactive: CheckCircle,
};

export function TrialBadge({
  trialInfo,
  showIcon = true,
  showDaysRemaining = true,
  size = 'md',
  className,
}: TrialBadgeProps) {
  const { t } = useTranslation('trials');
  const Icon = statusIcons[trialInfo.status];
  
  const displayText = getTrialDisplayText(trialInfo, t, { showDaysRemaining });

  return (
    <Badge
      variant="outline"
      className={cn(
        trialBadgeVariants({ status: trialInfo.status, size }),
        className
      )}
    >
      {showIcon && <Icon className="shrink-0" />}
      <span className="truncate">{displayText}</span>
    </Badge>
  );
}

interface TrialProgressBadgeProps {
  trialInfo: TrialInfo;
  showPercentage?: boolean;
  className?: string;
}

export function TrialProgressBadge({
  trialInfo,
  showPercentage = false,
  className,
}: TrialProgressBadgeProps) {
  if (trialInfo.status !== 'active' || !trialInfo.daysRemaining) {
    return <TrialBadge trialInfo={trialInfo} className={className} />;
  }

  const progressColor = (() => {
    if (trialInfo.progressPercentage >= 80) return 'bg-red-500';
    if (trialInfo.progressPercentage >= 60) return 'bg-yellow-500';
    return 'bg-blue-500';
  })();

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <TrialBadge trialInfo={trialInfo} showDaysRemaining={false} />
      <div className="flex items-center gap-1.5">
        <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700">
          <div
            className={cn('h-full transition-all duration-300', progressColor)}
            style={{ width: `${trialInfo.progressPercentage}%` }}
          />
        </div>
        {showPercentage && (
          <span className="text-xs text-muted-foreground font-medium">
            {Math.round(trialInfo.progressPercentage)}%
          </span>
        )}
      </div>
    </div>
  );
}

interface CompactTrialBadgeProps {
  trialInfo: TrialInfo;
  className?: string;
}

export function CompactTrialBadge({ trialInfo, className }: CompactTrialBadgeProps) {
  const { t } = useTranslation('trials');
  
  const displayText = getTrialDisplayText(trialInfo, t, { showDaysRemaining: true, compact: true });
  
  // Don't render if no display text (e.g., converted or inactive status)
  if (!displayText) {
    return null;
  }

  const isUrgent = trialInfo.status === 'active' && trialInfo.daysRemaining !== null && trialInfo.daysRemaining <= 1;
  const isExpired = trialInfo.status === 'expired';

  return (
    <Badge
      variant="outline"
      className={cn(
        'text-xs px-1.5 py-0.5 font-medium whitespace-nowrap',
        isExpired && 'bg-red-50 text-red-600 border-red-200 dark:bg-red-500/10 dark:text-red-400 dark:border-red-500/20',
        isUrgent && 'bg-yellow-50 text-yellow-600 border-yellow-200 dark:bg-yellow-500/10 dark:text-yellow-400 dark:border-yellow-500/20',
        !isExpired && !isUrgent && 'bg-blue-50 text-blue-600 border-blue-200 dark:bg-blue-500/10 dark:text-blue-400 dark:border-blue-500/20',
        className
      )}
    >
      {displayText}
    </Badge>
  );
}
