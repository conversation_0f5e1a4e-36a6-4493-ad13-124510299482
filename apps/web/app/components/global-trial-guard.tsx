'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useTrialStatus } from '../hooks/use-trial-status';
import { TrialExpirationModal } from './trial-expiration-modal';

interface GlobalTrialGuardProps {
  accountId: string;
  accountSlug: string;
  children: React.ReactNode;
}

export function GlobalTrialGuard({ accountId, accountSlug, children }: GlobalTrialGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { trialInfo, isLoading } = useTrialStatus({ accountId });
  const [showExpirationModal, setShowExpirationModal] = useState(false);

  // Handle trial expiration with modal-first approach
  useEffect(() => {
    // Don't process while loading
    if (isLoading) return;

    // Don't process if no trial info (personal accounts)
    if (!trialInfo) return;

    // Don't process if on allowed pages (billing, settings, account) or auth pages
    if (pathname.includes('/billing') ||
        pathname.includes('/settings') ||
        pathname.includes('/user-settings') ||
        pathname.includes('/auth')) return;

    // Don't process if trial is active or converted
    if (trialInfo.isActive || trialInfo.isConverted) return;

    // For expired trials, show modal first
    if (trialInfo.isExpired) {
      console.log('🚫 Trial expired - showing modal');
      setShowExpirationModal(true);
      return;
    }

    // Also handle inactive trials (shouldn't happen for team accounts, but safety check)
    if (trialInfo.status === 'inactive') {
      console.log('⚠️ Trial inactive - redirecting to billing page');
      router.replace(`/home/<USER>/billing`);
      return;
    }
  }, [trialInfo, isLoading, pathname, router, accountSlug]);

  // Handle modal upgrade action
  const handleUpgrade = () => {
    setShowExpirationModal(false);
    router.push(`/home/<USER>/billing`);
  };

  // Handle modal close/dismiss
  const handleModalClose = () => {
    setShowExpirationModal(false);
    // After modal is dismissed, redirect to billing page
    setTimeout(() => {
      router.replace(`/home/<USER>/billing`);
    }, 500);
  };

  // Show loading state while checking trial status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If trial is expired and not on allowed pages, show empty page with modal
  const isOnAllowedPage = pathname.includes('/billing') ||
                         pathname.includes('/settings') ||
                         pathname.includes('/user-settings');

  if (trialInfo?.isExpired && !isOnAllowedPage) {
    return (
      <div className="min-h-screen bg-background">
        {/* Empty page - content hidden when trial expired */}
        <TrialExpirationModal
          trialInfo={trialInfo}
          isOpen={showExpirationModal}
          onClose={handleModalClose}
          onUpgrade={handleUpgrade}
          onDismiss={handleModalClose}
          variant="expired"
        />
      </div>
    );
  }

  return (
    <>
      {children}

      {/* Trial Expiration Modal for other cases (warnings, etc.) */}
      {trialInfo && (
        <TrialExpirationModal
          trialInfo={trialInfo}
          isOpen={showExpirationModal}
          onClose={handleModalClose}
          onUpgrade={handleUpgrade}
          onDismiss={handleModalClose}
          variant="expired"
        />
      )}
    </>
  );
}
