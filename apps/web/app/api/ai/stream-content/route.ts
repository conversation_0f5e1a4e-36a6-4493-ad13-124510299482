import { NextRequest, NextResponse } from 'next/server';
import OpenA<PERSON> from 'openai';
import { observeOpenA<PERSON>, Langfuse } from 'langfuse';

// Optional Langfuse initialization for enhanced observability
const langfuse = process.env.LANGFUSE_SECRET_KEY ? new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
}) : null;

// CORS headers for the response
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
};

// Handle preflight OPTIONS requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders,
  });
}

export async function POST(request: NextRequest) {
  console.log('Processing OpenRouter AI request with streaming support');

  try {
    // Check authorization
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.AI_API_TOKEN || 'my-secret-token';
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401, headers: corsHeaders }
      );
    }

    const token = authHeader.split(' ')[1];
    if (token !== expectedToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401, headers: corsHeaders }
      );
    }

    // Check if OpenRouter API key is available
    if (!process.env.OPENROUTER_API_KEY) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500, headers: corsHeaders }
      );
    }

    // Parse the request body
    const requestBody = await request.json();
    console.log('Request body received:', { 
      model: 'google/gemini-2.0-flash-001', 
      stream: requestBody.stream,
      messagesCount: requestBody.messages?.length 
    });

    // Create OpenAI client with OpenRouter configuration
    const openai = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: process.env.OPENROUTER_API_KEY,
    });

    // Wrap with Langfuse for observability
    const langfuseClient = observeOpenAI(openai);

    // Check if streaming is requested
    if (requestBody.stream) {
      console.log('Handling streaming request');
      
      const stream = await langfuseClient.chat.completions.create({
        ...requestBody,
        temperature: 1,
        messages: requestBody.messages,
        stream: true,
        stream_options: { include_usage: true }
      }) as unknown as AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>;

      // Create a readable stream for the response
      const encoder = new TextEncoder();
      
      const readableStream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of stream) {
              const chunkData = `data: ${JSON.stringify(chunk)}\n\n`;
              console.log('Streaming chunk:', chunk.choices?.[0]?.delta?.content || '[metadata]');
              controller.enqueue(encoder.encode(chunkData));
            }
            
            // Send the final [DONE] message
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            
            // Flush Langfuse data
            await langfuseClient.flushAsync();
          } catch (error) {
            console.error('Error in streaming:', error);
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({ error: 'Stream error' })}\n\n`)
            );
          } finally {
            controller.close();
          }
        },
      });

      return new NextResponse(readableStream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          ...corsHeaders,
        },
      });
    } else {
      console.log('Handling non-streaming request');
      
      const response = await langfuseClient.chat.completions.create({
        ...requestBody,
        stream: false
      });

      await langfuseClient.flushAsync();
      
      return NextResponse.json(response, {
        headers: corsHeaders,
      });
    }
  } catch (error) {
    console.error('Error processing AI request:', error);
    return NextResponse.json(
      { error: 'Failed to process AI request' },
      { status: 500, headers: corsHeaders }
    );
  }
}
