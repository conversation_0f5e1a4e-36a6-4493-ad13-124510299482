import { getPlanTypesMap } from '@kit/billing';
import { getBillingEventHandlerService } from '@kit/billing-gateway';
import { enhanceRouteHandler } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import billingConfig from '~/config/billing.config';

/**
 * @description Handle the webhooks from Stripe related to checkouts
 */
export const POST = enhanceRouteHandler(
  async ({ request }) => {
    const provider = billingConfig.provider;
    const logger = await getLogger();

    const ctx = {
      name: 'billing.webhook',
      provider,
    };

    logger.info(ctx, `Received billing webhook. Processing...`);

    // Log the request headers and body for debugging
    const headers = Object.fromEntries(request.headers.entries());
    logger.info({
      ...ctx,
      headers: JSON.stringify(headers),
    }, 'Webhook request headers');

    try {
      const body = await request.clone().text();
      logger.info({
        ...ctx,
        bodyLength: body.length,
        bodyPreview: body.substring(0, 200),
      }, 'Webhook request body');
      
      // Try to parse the body as JSON to see what event type it is
      try {
        const eventData = JSON.parse(body);
        logger.info({
          ...ctx,
          eventType: eventData.type,
        }, 'Webhook event type');
      } catch (parseError) {
        logger.error({
          ...ctx,
          parseError,
        }, 'Failed to parse webhook body as JSON');
      }
    } catch (error) {
      logger.error({
        ...ctx,
        error,
      }, 'Failed to read webhook body');
    }

    const supabaseClientProvider = () => getSupabaseServerAdminClient();

    const service = await getBillingEventHandlerService(
      supabaseClientProvider,
      provider,
      getPlanTypesMap(billingConfig),
    );

    try {
      // Read the body once and pass it to the service
      const body = await request.text();
      await service.handleWebhookEvent(request, {}, body);

      logger.info(ctx, `Successfully processed billing webhook`);

      return new Response('OK', { status: 200 });
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to process billing webhook`);

      return new Response('Failed to process billing webhook', {
        status: 500,
      });
    }
  },
  {
    auth: false,
  },
);
