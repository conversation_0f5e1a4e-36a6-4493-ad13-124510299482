'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Checkbox } from '@kit/ui/checkbox';
import { Badge } from '@kit/ui/badge';
import { Loader2 } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import { useICPs, useResearchGeneration, type GeneratedResearch } from '../_lib/hooks/use-market-research-data';
import { ICPPersonaSelector } from './shared/icp-persona-selector';

const SOCIAL_PLATFORMS = [
  { value: 'twitter', label: 'Twitter/X' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'reddit', label: 'Reddit' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'instagram', label: 'Instagram' },
];

const TIME_FILTERS = [
  'Last 3 months',
  'Last 6 months', 
  'Last 12 months'
];

interface SocialResearchFormData {
  icpId: string;
  personaId: string;
  platforms: string[];
  keywords: string;
  timeFilter: string;
  includeComments: boolean;
  includeEngagement: boolean;
}

interface SocialResearchProps {
  selectedResearch?: GeneratedResearch | null;
  onResearchSaved?: (research: GeneratedResearch) => void;
  isGenerating?: boolean;
}

export function SocialResearch({ selectedResearch, onResearchSaved, isGenerating }: SocialResearchProps) {
  const [pending, startTransition] = useTransition();
  const [formData, setFormData] = useState<SocialResearchFormData>({
    icpId: selectedResearch?.icp_id || '',
    personaId: selectedResearch?.persona_id || '',
    platforms: ['twitter', 'linkedin'],
    keywords: '',
    timeFilter: 'Last 3 months',
    includeComments: true,
    includeEngagement: true
  });

  const { data: icps = [] } = useICPs();
  const { generateWebResearch } = useResearchGeneration();

  const handleICPChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      icpId: value,
      personaId: ''
    }));
  };

  const handlePersonaChange = (value: string) => {
    setFormData(prev => ({ ...prev, personaId: value }));
  };

  const handlePlatformChange = (platform: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      platforms: checked
        ? [...prev.platforms, platform]
        : prev.platforms.filter(p => p !== platform)
    }));
  };

  const handleSubmit = () => {
    if (!isFormValid) return;

    startTransition(async () => {
      try {
        const selectedICP = icps.find(icp => icp.id === formData.icpId);
        const platformNames = formData.platforms.map(p => 
          SOCIAL_PLATFORMS.find(platform => platform.value === p)?.label || p
        ).join(', ');
        
        const research = await generateWebResearch({
          icpId: formData.icpId,
          personaId: formData.personaId,
          timeFilter: formData.timeFilter,
          type: 'social-media-research',
          topic: `Social media research on ${platformNames} for keywords: ${formData.keywords}`,
          icpName: selectedICP?.name || 'Unknown ICP',
          typeName: 'Social Media Research',
        });

        onResearchSaved?.(research);
      } catch (error) {
        console.error('Error generating social research:', error);
        toast.error('Failed to generate social research', {
          description: error instanceof Error ? error.message : 'An unexpected error occurred'
        });
      }
    });
  };

  const isFormValid = formData.icpId && formData.platforms.length > 0 && formData.keywords.trim();

  const selectedICP = icps.find(icp => icp.id === formData.icpId);

  return (
    <div className="space-y-6">
      {/* ICP and Persona Selection */}
      <ICPPersonaSelector
        icpId={formData.icpId}
        personaId={formData.personaId}
        onICPChange={handleICPChange}
        onPersonaChange={handlePersonaChange}
      />

      {/* Keywords Input */}
      <div className="space-y-2">
        <Label htmlFor="keywords-input">Keywords</Label>
        <Input
          id="keywords-input"
          type="text"
          placeholder="Enter keywords to search for (e.g., 'AI tools', 'marketing automation')"
          value={formData.keywords}
          onChange={(e) => setFormData(prev => ({ ...prev, keywords: e.target.value }))}
          className="w-full"
        />
      </div>

      {/* Platform Selection */}
      <div className="space-y-3">
        <Label>Social Media Platforms</Label>
        <div className="grid grid-cols-2 gap-3">
          {SOCIAL_PLATFORMS.map(platform => (
            <div key={platform.value} className="flex items-center space-x-2">
              <Checkbox
                id={platform.value}
                checked={formData.platforms.includes(platform.value)}
                onCheckedChange={(checked) => handlePlatformChange(platform.value, checked as boolean)}
              />
              <Label htmlFor={platform.value} className="cursor-pointer">
                {platform.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Time Filter */}
      <div className="space-y-2">
        <Label htmlFor="time-filter">Time Filter</Label>
        <Select 
          value={formData.timeFilter} 
          onValueChange={(value) => setFormData(prev => ({ ...prev, timeFilter: value }))}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select time period" />
          </SelectTrigger>
          <SelectContent>
            {TIME_FILTERS.map(filter => (
              <SelectItem key={filter} value={filter}>
                {filter}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Additional Options */}
      <div className="space-y-3">
        <Label>Additional Options</Label>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-comments"
              checked={formData.includeComments}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, includeComments: checked as boolean }))}
            />
            <Label htmlFor="include-comments" className="cursor-pointer">
              Include comments and replies
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-engagement"
              checked={formData.includeEngagement}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, includeEngagement: checked as boolean }))}
            />
            <Label htmlFor="include-engagement" className="cursor-pointer">
              Include engagement metrics
            </Label>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <Button 
        onClick={handleSubmit}
        disabled={!isFormValid || pending || isGenerating}
        className="w-full"
        size="lg"
      >
        {pending || isGenerating ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Generating Social Research...
          </div>
        ) : (
          'Generate Social Research'
        )}
      </Button>

      {/* Selected Configuration Summary */}
      {(selectedICP || formData.platforms.length > 0) && (
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <h4 className="font-medium text-sm">Research Configuration:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedICP && (
              <Badge variant="outline">ICP: {selectedICP.name}</Badge>
            )}
            {formData.platforms.length > 0 && (
              <Badge variant="outline">
                Platforms: {formData.platforms.map(p => 
                  SOCIAL_PLATFORMS.find(platform => platform.value === p)?.label || p
                ).join(', ')}
              </Badge>
            )}
            {formData.keywords && (
              <Badge variant="outline">Keywords: {formData.keywords}</Badge>
            )}
            {formData.timeFilter && (
              <Badge variant="outline">Period: {formData.timeFilter}</Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

