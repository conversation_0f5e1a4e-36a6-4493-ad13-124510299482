'use client';

import { <PERSON><PERSON> } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Badge } from '@kit/ui/badge';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery } from '@tanstack/react-query';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Plus, MessageSquare, Calendar, Users, Target, Globe, Loader2 } from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { Trans } from '@kit/ui/trans';
import { useZero } from '~/hooks/use-zero';
import { useUser } from '@kit/supabase/hooks/use-user';

interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string;
  research_type: 'pain-points' | 'trending-topics' | 'recent-news';
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

interface SiteResearch {
  id: string;
  company_id: string;
  created_at: number;
  icps: string[];
  personal: string[];
  urls: string[];
  instruction: string;
  schema: Record<string, any>;
  enable_web_search: boolean;
  agent_mode: boolean;
  is_generating: boolean;
  error_generating: boolean;
  results: Record<string, any>[];
}

interface ResearchSidebarProps {
  selectedResearchId?: string;
  selectedSiteResearchId?: string;
  onSelectResearch: (research: GeneratedResearch | null) => void;
  onSelectSiteResearch: (research: SiteResearch | null) => void;
  onNewResearch: () => void;
}

function useGeneratedResearch(accountId: string) {
  const supabase = useSupabase();
  
  return useQuery({
    queryKey: ['generated-research', accountId],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('generated_research')
          .select('*')
          .eq('account_id', accountId)
          .order('created_at', { ascending: false });

        if (error) {
          // If table doesn't exist yet, return empty array
          if (error.message.includes('relation "public.generated_research" does not exist')) {
            return [];
          }
          throw new Error(`Failed to fetch research history: ${error.message}`);
        }

        return (data || []) as GeneratedResearch[];
      } catch (error) {
        // Handle case where table doesn't exist yet
        console.warn('Generated research table not available yet:', error);
        return [];
      }
    },
    enabled: !!accountId,
  });
}



const RESEARCH_TYPE_LABELS = {
  'pain-points': 'Pain Points',
  'trending-topics': 'Trending Topics',
  'recent-news': 'Recent News'
} as const;

const RESEARCH_TYPE_ICONS = {
  'pain-points': '🎯',
  'trending-topics': '📈',
  'recent-news': '📰'
} as const;

export function ResearchSidebar({ 
  selectedResearchId,
  selectedSiteResearchId,
  onSelectResearch,
  onSelectSiteResearch,
  onNewResearch 
}: ResearchSidebarProps) {
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const zero = useZero();

  const { data: researchHistory = [], isLoading } = useGeneratedResearch(accountId);
  
  // Get site research using Zero query
  const [siteResearchHistory] = useZeroQuery(
    zero.query.site_research.where('company_id', accountId),
    { ttl: '1d' }
  );



  const formatDate = (dateString: string | number) => {
    const date = typeof dateString === 'number' ? new Date(dateString) : new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const generateTitle = (research: GeneratedResearch) => {
    const typeLabel = RESEARCH_TYPE_LABELS[research.research_type];
    return research.title || `${typeLabel} Research`;
  };

  // Combine and sort all research items by date
  const allResearchItems = [
    ...(researchHistory || []).map(item => ({ ...item, type: 'regular' as const })),
    ...(siteResearchHistory || []).map(item => ({ ...item, type: 'site' as const }))
  ].sort((a, b) => {
    const dateA = a.type === 'regular' ? new Date(a.created_at).getTime() : a.created_at;
    const dateB = b.type === 'regular' ? new Date(b.created_at).getTime() : b.created_at;
    return dateB - dateA; // Sort descending (newest first)
  });

  return (
    <div className="w-80 border-r bg-muted/30 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <Button
          onClick={onNewResearch}
          className="w-full justify-start gap-2"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
          <Trans i18nKey="marketResearch:newResearch" defaults="New Research" />
        </Button>
      </div>

      {/* Research History */}
      <div className="flex-1 overflow-hidden">
        <div className="p-4 pb-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            <Trans i18nKey="marketResearch:researchHistory" defaults="Research History" />
          </h3>
        </div>

        <ScrollArea className="flex-1 px-2">
          {isLoading ? (
            <div className="p-4 space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-muted rounded-lg animate-pulse" />
              ))}
            </div>
          ) : allResearchItems.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <Trans 
                i18nKey="marketResearch:noHistory" 
                defaults="No research history yet. Start by creating your first research session." 
              />
            </div>
          ) : (
            <div className="space-y-1 pb-4">
              {allResearchItems.map((item) => {
                if (item.type === 'regular') {
                  const research = item as GeneratedResearch & { type: 'regular' };
                  return (
                    <Card
                      key={`regular-${research.id}`}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:bg-accent/50",
                        selectedResearchId === research.id && "bg-accent border-primary/50"
                      )}
                      onClick={() => {
                        onSelectResearch(research);
                      }}
                    >
                      <CardContent className="p-3 space-y-2">
                        {/* Title and Type */}
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium truncate">
                                {research.topic || RESEARCH_TYPE_LABELS[research.research_type]}
                            </h4>
                            <Badge variant="outline" className="text-xs mt-1">
                              {RESEARCH_TYPE_ICONS[research.research_type]} {generateTitle(research)}
                            </Badge>
                          </div>
                        </div>

                        {/* Metadata */}
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{research.time_filter}</span>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-1">
                                <Target className="h-3 w-3" />
                                <span>{research.results?.length || 0}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                <span>{research.content_suggestions?.length || 0}</span>
                              </div>
                            </div>
                            <span>{formatDate(research.created_at)}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                } else {
                  const siteResearch = item as SiteResearch & { type: 'site' };
                  return (
                    <Card
                      key={`site-${siteResearch.id}`}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:bg-accent/50",
                        selectedSiteResearchId === siteResearch.id && "bg-accent border-primary/50"
                      )}
                      onClick={() => {
                        onSelectSiteResearch(siteResearch);
                      }}
                    >
                      <CardContent className="p-3 space-y-2">
                        {/* Title and Type */}
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="text-sm font-medium truncate">
                                🌐 Site Analysis
                              </h4>
                              {siteResearch.is_generating && (
                                <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                              )}
                            </div>
                            <div className="flex gap-2 mt-1">
                              <Badge variant="secondary" className="text-xs">
                                SITE RESEARCH
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                <Globe className="h-3 w-3 mr-1" />
                                {siteResearch.urls?.length || 0} URLs
                              </Badge>
                            </div>
                          </div>
                        </div>

                        {/* Metadata */}
                        <div className="space-y-1 text-xs text-muted-foreground">
                          {siteResearch.instruction && (
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-3 w-3" />
                              <span className="truncate">{siteResearch.instruction}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-1">
                                <Target className="h-3 w-3" />
                                <span>
                                  {siteResearch.is_generating 
                                    ? 'Generating...' 
                                    : `${siteResearch.results?.length || 0} records`
                                  }
                                </span>
                              </div>
                              {siteResearch.enable_web_search && (
                                <div className="flex items-center gap-1">
                                  <Globe className="h-3 w-3" />
                                  <span>Web Search</span>
                                </div>
                              )}
                            </div>
                            <span>{formatDate(siteResearch.created_at)}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                }
              })}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
} 