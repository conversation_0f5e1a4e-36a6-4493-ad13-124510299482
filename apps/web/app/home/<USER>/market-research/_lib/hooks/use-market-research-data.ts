import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useUser } from '@kit/supabase/hooks/use-user';
import { useZero } from '~/hooks/use-zero';
import { toast } from '@kit/ui/sonner';

export interface ICP {
  id: string;
  name: string;
}

export interface Persona {
  id: string;
  name: string;
  role: string;
}

export interface GeneratedResearch {
  id: string;
  account_id: string;
  icp_id: string;
  persona_id: string | null;
  research_type: 'pain-points' | 'trending-topics' | 'recent-news';
  time_filter: string;
  title: string;
  results: any[];
  content_suggestions: any[];
  created_at: string;
  updated_at: string;
  topic?: string;
}

export interface SiteResearch {
  id: string;
  company_id: string;
  created_at: number;
  icps: string[];
  personal: string[];
  urls: string[];
  instruction: string;
  schema: Record<string, any>;
  enable_web_search: boolean;
  agent_mode: boolean;
  is_generating: boolean;
  error_generating: boolean;
  results: Record<string, any>[];
}

export function useICPs() {
  const supabase = useSupabase();
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';

  return useQuery({
    queryKey: ['icps', accountId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('icps')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (error) {
        throw new Error(`Failed to fetch ICPs: ${error.message}`);
      }

      return data as ICP[];
    },
    enabled: !!accountId,
  });
}

export function usePersonas(icpId?: string) {
  const supabase = useSupabase();
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  
  return useQuery({
    queryKey: ['personas', accountId, icpId],
    queryFn: async () => {
      let query = supabase
        .from('personas')
        .select('*')
        .eq('company_id', accountId)
        .order('name');

      if (icpId) {
        query = query.eq('icp_id', icpId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch personas: ${error.message}`);
      }

      return data as Persona[];
    },
    enabled: !!accountId,
  });
}

export function useResearchGeneration() {
  const { account } = useTeamAccountWorkspace();
  const accountId = account?.id || '';
  const { data: user } = useUser();
  const zero = useZero();

  const generateWebResearch = async (params: {
    icpId: string;
    personaId: string;
    timeFilter: string;
    type: string;
    topic: string;
    icpName: string;
    typeName: string;
  }) => {
    if (!zero || !user) {
      throw new Error('Missing dependencies for research generation');
    }

    const researchId = crypto.randomUUID();
    const researchTitle = `${params.typeName} - ${params.icpName}`;

    zero.mutate.generated_research.insert({
      id: researchId,
      account_id: accountId,
      icp_id: params.icpId,
      persona_id: params.personaId === 'no-persona' ? null : params.personaId,
      research_type: params.type,
      time_filter: params.timeFilter,
      title: researchTitle,
      topic: params.topic,
      created_by: user.id,
    });

    const placeholderResearch: GeneratedResearch = {
      id: researchId,
      account_id: accountId,
      icp_id: params.icpId,
      persona_id: params.personaId === 'no-persona' ? null : params.personaId,
      research_type: params.type as 'pain-points' | 'trending-topics' | 'recent-news',
      time_filter: params.timeFilter,
      title: researchTitle,
      results: [],
      content_suggestions: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      topic: params.topic,
    };

    toast.success('Research generation started!', {
      description: 'Your research is being generated in the background. Results will appear shortly.'
    });

    return placeholderResearch;
  };

  const generateSiteResearch = async (params: {
    icpId: string;
    personaId: string;
    urls: string[];
    instructions: string;
    schema: Record<string, any>;
    allowWebSearch: boolean;
    agentMode: boolean;
  }) => {
    if (!zero || !user) {
      throw new Error('Missing dependencies for site research generation');
    }

    const researchId = crypto.randomUUID();

    zero.mutate.site_research.insert({
      id: researchId,
      values: {
        company_id: accountId,
        icps: [params.icpId],
        personal: [params.personaId],
        urls: params.urls,
        instruction: params.instructions,
        schema: params.schema,
        enable_web_search: params.allowWebSearch,
        agent_mode: params.agentMode,
        is_generating: true,
        error_generating: false,
        results: [],
      }
    });

    const placeholderSiteResearch: SiteResearch = {
      id: researchId,
      company_id: accountId,
      created_at: Date.now(),
      icps: [params.icpId],
      personal: [params.personaId],
      urls: params.urls,
      instruction: params.instructions,
      schema: params.schema,
      enable_web_search: params.allowWebSearch,
      agent_mode: params.agentMode,
      is_generating: true,
      error_generating: false,
      results: [],
    };

    toast.success('Site analysis generation started!', {
      description: 'Your site analysis is being generated in the background.'
    });

    return placeholderSiteResearch;
  };

  return {
    generateWebResearch,
    generateSiteResearch,
  };
} 