import { Button } from '@kit/ui/button';
import { cn } from '@kit/ui/utils';
import { CheckCircle } from 'lucide-react';
import { VideoDialog } from './video-dialog';

interface OnboardingTaskItemProps {
  title: string;
  completed: boolean;
  onToggle: () => void;
  videoUrl: string;
}

export function OnboardingTaskItem({
  title,
  completed,
  onToggle,
  videoUrl,
}: OnboardingTaskItemProps) {
  return (
    <div className="flex items-center gap-3 group">
      <VideoDialog title={title} videoUrl={videoUrl}>
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-auto hover:bg-transparent"
          onClick={onToggle}
        >
          <div className="relative">
            {completed ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <div className="h-5 w-5 border-2 border-dashed border-muted-foreground rounded-full group-hover:border-primary transition-colors" />
            )}
          </div>
        </Button>
      </VideoDialog>
      
      <VideoDialog title={title} videoUrl={videoUrl}>
        <Button
          variant="ghost"
          className={cn(
            'flex-1 justify-start p-0 h-auto font-medium text-left hover:bg-transparent',
            completed ? 'text-green-600 line-through' : 'text-foreground group-hover:text-primary'
          )}
        >
          {title}
        </Button>
      </VideoDialog>
    </div>
  );
} 