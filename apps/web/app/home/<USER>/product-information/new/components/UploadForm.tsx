'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { useMutation } from '@tanstack/react-query';
import { Cloud, Link } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';

import { uploadProductDocument } from '~/services/product-document';
import { getLocalApi } from '~/utils/api.util';
import { getFormattedUrl } from '~/utils/url.util';

// Import Vercel Blob client
import { upload } from '@vercel/blob/client';

import UrlDialog from './UrlDialog';

interface UploadFormInputs {
  title: string;
  document: File | null;
}

export default function UploadForm() {
  const { account } = useTeamAccountWorkspace();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<File[]>([]);
  const [isOpen, onOpenChange] = useState(false);

  const { handleSubmit, setValue, watch } = useForm<UploadFormInputs>({
    defaultValues: {
      title: '',
      document: null,
    },
  });

  const title = watch('title');

  const { getRootProps, getInputProps, isDragAccept, isDragReject } =
    useDropzone({
      accept: {
        'application/pdf': ['.pdf'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          ['.docx'],
      },
      maxFiles: 1,
      onDropAccepted: (acceptedFiles) => {
        if (acceptedFiles.length < 0) {
          console.log('No files selected');
          return;
        }

        setFiles(acceptedFiles);
        setValue('document', acceptedFiles[0] as File);

        const fileName = (acceptedFiles[0] as File).name;
        const fileNameWithoutExt =
          fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
        console.log({ fileName, fileNameWithoutExt });
        setValue('title', fileNameWithoutExt);
      },
    });

  const uploadMutation = useMutation({
    mutationFn: async (data: { title: string; file: File }) => {
      try {
        // Upload file to Vercel Blob using product document upload route
        const blob = await upload(data.file.name, data.file, {
          access: 'public',
          handleUploadUrl: '/api/product-documents/upload',
        });

        // Upload product document with blob URL
        return uploadProductDocument(data.title, null, account?.id, undefined, blob.url);
      } catch (error) {
        console.error('Error uploading to blob:', error);
        throw new Error('Failed to upload document');
      }
    },
    onSuccess: () => {
      toast.success('Document uploaded successfully');
      router.push(`/home/<USER>/product-information`);
      router.refresh();
    },
    onError: (error: any) => {
      console.error('Upload error:', error);
      toast.error(error.message || 'Failed to upload document');
      setError(error.message);
    },
  });

  const urlMutation = useMutation({
    mutationFn: async (url: string) => {
      url = getFormattedUrl(url);
      console.log("URL", url);
      const response = await getLocalApi().post('/scrape-website', { url });

      if (!response.data.text) {
        throw new Error('Failed to scrape website');
      }
      console.log("RESPONSE", response.data);
      const content = response.data.text as string;
      console.log("CONTENT", content);
      return uploadProductDocument(url, null, account?.id, content, undefined);
    },
    onSuccess: () => {
      toast.success('Content extracted and uploaded successfully');
      router.push(`/home/<USER>/product-information`);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to extract content from URL');
      setError(error.message);
    },
  });

  const onSubmit = async (data: UploadFormInputs) => {
    if (data.document) {
      uploadMutation.mutate({
        title: data.title,
        file: data.document,
      });
    }
  };

  const handleUrlSubmit = async (url: string) => {
    await urlMutation.mutateAsync(url);
  };

  return (
    <>
      <Card className="mx-auto max-w-2xl">
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="mb-4 flex justify-end">
              <Button
                variant="default"
                onClick={() => {
                  onOpenChange(true);
                }}
              >
                <Link className="mr-2 h-4 w-4" />
                Pull from URL instead
              </Button>
            </div>

            <div
              {...getRootProps()}
              className={`hover:border-primary cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${isDragAccept ? 'border-green-500 bg-green-50' : ''} ${isDragReject ? 'border-red-500 bg-red-50' : ''} `}
            >
              <input {...getInputProps()} />
              <div className="flex flex-col items-center space-y-4">
                <Cloud className="h-12 w-12 text-gray-400" />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">
                    Drag and drop your document here
                  </h3>
                  <p className="text-sm text-gray-500">
                    or click to select files
                  </p>
                </div>
                {files.length > 0 && (
                  <div className="text-sm text-gray-500">
                    Selected: {(files[0] as File).name}
                  </div>
                )}
              </div>
            </div>

            <div className="p-4">
              <Label htmlFor="url">Title</Label>
              <Input
                placeholder="Enter document title"
                value={title}
                onChange={(e) => setValue('title', e.target.value)}
                className={error ? 'border-red-500' : ''}
              />
            </div>

            {error && <div className="text-sm text-red-500">{error}</div>}

            <div className="flex justify-end">
              <Button
                type="submit"
                color="primary"
                disabled={files.length === 0 ||  uploadMutation.isPending}
              >
                {uploadMutation.isPending ? 'Uploading...' : 'Upload Document'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <UrlDialog
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        onSubmit={handleUrlSubmit}
        isLoading={urlMutation.isPending}
      />
    </>
  );
}
