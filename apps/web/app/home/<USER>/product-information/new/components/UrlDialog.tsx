import { useState } from 'react';

import { But<PERSON> } from '@kit/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';

interface UrlDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSubmit: (url: string) => Promise<void>;
  isLoading: boolean;
}

export default function UrlDialog({
  isOpen,
  onOpenChange,
  onSubmit,
  isLoading,
}: UrlDialogProps) {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async () => {
   
    console.log("URL", url);
    if (!url) {
      setError('Please enter a URL');
      return;
    }

    try {
      // const urlObj = new URL(url);
      setError('');
      await onSubmit(url);
      setUrl('');
    } catch (e) {
      console.log({ e });
      setError('Please enter a valid URL');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Enter Website URL</DialogTitle>
        </DialogHeader>
        <div className="p-4">
          <Label htmlFor="url">URL</Label>
          <Input
            type="url"
            id="url"
            placeholder="https://example.com"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className={error ? 'border-red-500' : ''}
          />
          {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Pulling...' : 'Pull Content'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
