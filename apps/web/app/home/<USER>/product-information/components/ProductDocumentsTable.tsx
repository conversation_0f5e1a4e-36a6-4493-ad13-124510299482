'use client';

import { useState, useTransition } from 'react';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Edit, Trash2, MoreVertical } from 'lucide-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { DataTable } from '@kit/ui/enhanced-data-table';
import { Button } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { toast } from '@kit/ui/sonner';

import { ProductDocumentWithLink } from '~/types/product-document';
import { updateProductDocumentTitle, deleteProductDocument } from '~/services/product-document';

import { PDFViewerModal } from './PDFViewerModal';

interface ProductDocumentsTableProps {
  data: ProductDocumentWithLink[];
  page: number;
  pageCount: number;
  pageSize: number;
  url?: string;
}

export function ProductDocumentsTable({
  data,
  page,
  pageSize,
  pageCount,
}: ProductDocumentsTableProps) {
  const [selectedFile, setSelectedFile] = useState<ProductDocumentWithLink | null>(
    null,
  );
  const [renameDialog, setRenameDialog] = useState<{
    isOpen: boolean;
    document: ProductDocumentWithLink | null;
  }>({ isOpen: false, document: null });
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    document: ProductDocumentWithLink | null;
  }>({ isOpen: false, document: null });
  const [newTitle, setNewTitle] = useState('');
  const [isPending, startTransition] = useTransition();

  const queryClient = useQueryClient();

  const renameMutation = useMutation({
    mutationFn: async ({ documentId, title }: { documentId: string; title: string }) => {
      return updateProductDocumentTitle(documentId, title);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productDocuments'] });
      setRenameDialog({ isOpen: false, document: null });
      setNewTitle('');
      toast.success('Document renamed successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to rename document');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (documentId: string) => {
      return deleteProductDocument(documentId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['productDocuments'] });
      setDeleteDialog({ isOpen: false, document: null });
      toast.success('Document deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to delete document');
    },
  });

  const handleRename = (document: ProductDocumentWithLink) => {
    setRenameDialog({ isOpen: true, document });
    setNewTitle(document.title);
  };

  const handleDelete = (document: ProductDocumentWithLink) => {
    setDeleteDialog({ isOpen: true, document });
  };

  const confirmRename = () => {
    if (renameDialog.document && newTitle.trim()) {
      startTransition(() => {
        renameMutation.mutate({
          documentId: renameDialog.document!.id!,
          title: newTitle.trim(),
        });
      });
    }
  };

  const confirmDelete = () => {
    if (deleteDialog.document) {
      startTransition(() => {
        deleteMutation.mutate(deleteDialog.document!.id!);
      });
    }
  };

  const columns: ColumnDef<ProductDocumentWithLink>[] = [
    {
      id: 'title',
      header: 'Name',
      cell: ({ row }) => {
        return (
          <p
            className="h-auto cursor-pointer p-0 font-medium hover:text-blue-700"
            onClick={() => setSelectedFile(row.original)}
          >
            {row.original.title}
          </p>
        );
      },
    },
    {
      id: 'createdAt',
      header: 'Created At',
      cell: ({ row }) => {
        return format(new Date(row.original.created_at || ''), 'PPp');
      },
    },
    {
      id: 'actions',
      header: '',
      cell: ({ row }) => {
        return (
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleRename(row.original)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => handleDelete(row.original)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
  console.log({selectedFile});

  return (
    <>
      <DataTable
        data={data}
        columns={columns}
        pageIndex={page - 1}
        pageCount={pageCount}
        pageSize={pageSize}
      />
      
      <PDFViewerModal
        isOpen={!!selectedFile}
        onClose={() => setSelectedFile(null)}
        fileUrl={selectedFile?.file_path ?? ''} // Use url instead of path
        fileName={selectedFile?.title ?? ''}
        content={selectedFile?.content ?? ''}
        fileType={selectedFile?.file_type ?? ''}
      />

      {/* Rename Dialog */}
      <Dialog open={renameDialog.isOpen} onOpenChange={(open) => {
        if (!open) {
          setRenameDialog({ isOpen: false, document: null });
          setNewTitle('');
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Document</DialogTitle>
            <DialogDescription>
              Enter a new name for &quot;{renameDialog.document?.title}&quot;
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Name
              </Label>
              <Input
                id="title"
                value={newTitle}
                onChange={(e) => setNewTitle(e.target.value)}
                className="col-span-3"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    confirmRename();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setRenameDialog({ isOpen: false, document: null });
                setNewTitle('');
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={confirmRename}
              disabled={!newTitle.trim() || isPending}
            >
              {isPending ? 'Renaming...' : 'Rename'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialog.isOpen} onOpenChange={(open) => {
        if (!open) {
          setDeleteDialog({ isOpen: false, document: null });
        }
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the document
              &quot;{deleteDialog.document?.title}&quot; and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
