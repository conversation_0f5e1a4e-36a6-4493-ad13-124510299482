'use client';

import React from 'react';

import Link from 'next/link';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import EmptyState from '~/components/empty-state';

import { ProductDocumentsTable } from './ProductDocumentsTable';
import { useQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { ProductDocument } from '~/types/product-document';

export default function ProductInfoContent() {

  const { account } = useTeamAccountWorkspace();
  const z = useZero();
  
  const [product_documents] = useQuery(z.query.product_documents, {
    ttl: "10m"
  });
  
  console.log({product_documents})
  if (!product_documents?.length) {
    return (
      <div className="flex grow flex-row items-center justify-center">
        <EmptyState
          iconName="Package"
          title={'Product Information.'}
          description={
            'Teach the system all about your products by uploading your documentation here. You can also upload other documents like customer testimonials, blog posts, go to market plans and more.'
          }
          buttonText={'Add Products'}
          action={`/home/<USER>/product-information/new`}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-10 py-10">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Product Documents</h1>
        <Link
          href={`/home/<USER>/product-information/new`}
          className="inline-flex items-center rounded-lg bg-blue-500 px-4 py-2 text-sm font-medium text-white transition-colors duration-150 hover:bg-blue-600"
        >
          Add Documents
        </Link>
      </div>
      <ProductDocumentsTable
        data={product_documents as unknown as ProductDocument[]}
        page={1}
        pageCount={1}
        pageSize={20}
      />
    </div>
  );
}
