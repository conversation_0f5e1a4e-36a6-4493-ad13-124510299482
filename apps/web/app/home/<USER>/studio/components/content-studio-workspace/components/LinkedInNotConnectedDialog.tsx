'use client';

import { <PERSON><PERSON> } from "@kit/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@kit/ui/dialog";
import { useRouter } from "next/navigation";
import { Linkedin } from "lucide-react";

interface LinkedInNotConnectedDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountSlug: string;
}

export function LinkedInNotConnectedDialog({
  open,
  onOpenChange,
  accountSlug,
}: LinkedInNotConnectedDialogProps) {
  const router = useRouter();

  const handleGoToIntegrations = () => {
    router.push(`/home/<USER>/integrations`);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Linkedin className="h-5 w-5 text-[#0077B5]" />
            LinkedIn Connection Required
          </DialogTitle>
          <DialogDescription>
            You need to connect your LinkedIn account to use this feature.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            Connect your LinkedIn account to share updates, view your profile information, and engage with your professional network.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleGoToIntegrations}>
            Go to Integrations
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 