import React from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { ArrowLeft } from "lucide-react";
import { VideoSubmenu } from './VideoSubmenu';
import { ExistingVideoSelector } from './ExistingVideoSelector';
import { VideoUploader } from './VideoUploader';
import { SelectedVideoEditor } from './SelectedVideoEditor';
import { useVideoContent, useBaseContent } from '../../ContentStudioContext';
import DbVideoEditor from "../../../video-editor/components/editor/db-video-editor";
import { useQueryClient } from '@tanstack/react-query';

export const VideoContentEditor: React.FC = () => {
  // Get state from context
  const {
    selectedVideoOption,
    setSelectedVideoOption,
    setVideoEditorSaved,
    setActiveVideoContent,
    setVideoEditorContentId
  } = useVideoContent();

  const { companyContent } = useBaseContent();

  // Handle content saved callback
  const handleContentSaved = async (id: string) => {
    // Invalidate the query to refresh data
    
    // Update video editor state in context
    setVideoEditorSaved(new Date());
    
  };
  
  // Check if we have valid content with ID
  const contentAvailable = !!companyContent?.id;
  
  // Log content for debugging
  React.useEffect(() => {
    console.log("VideoContentEditor - Company Content:", {
      id: companyContent?.id,
      content_type: companyContent?.content_type,
      has_video_editor_data: !!companyContent?.video_editor_overlays,
      overlays_count: companyContent?.video_editor_overlays?.length,
      // from_context: !!campaignContext?.companyContent?.id,
    });
    
    // If content changes (like after a refresh/load), update the context
    if (companyContent?.id) {
      setVideoEditorContentId(companyContent.id);
      setActiveVideoContent(companyContent);
    }
  }, [companyContent, setVideoEditorContentId, setActiveVideoContent]);
  
  // If no option is selected, show the submenu
  if (selectedVideoOption === null) {
    return (
      <div className="space-y-6">
        <h2 className="text-lg font-semibold">Video Editor</h2>
        <VideoSubmenu />
      </div>
    );
  }

  // Show the selected option's component
  console.log("selectedVideoOption", selectedVideoOption);
  switch (selectedVideoOption) {
    case 'existing':
      return <ExistingVideoSelector 
               onUploadClick={() => setSelectedVideoOption('upload')}
               onBackToChoice={() => setSelectedVideoOption(null)}
             />;
    case 'create':
      // Show the video editor if we have content
      return contentAvailable ? (
        <div className="h-full w-full">
          <DbVideoEditor 
            contentId={companyContent.id}
            companyContent={companyContent}
            onContentSaved={handleContentSaved}
          />
        </div>
      ) : (
        <div className="flex justify-center items-center h-96">
          <div className="flex flex-col items-center space-y-4">
            <p>No content available to edit. Please go back and select a different option.</p>
            <Button
              variant="ghost"
              onClick={() => setSelectedVideoOption(null)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        </div>
      );
    case 'upload':
      return <VideoUploader />;
    case 'selected':
      return <SelectedVideoEditor />;
    default:
      return (
        <div className="space-y-4">
          <Button
            variant="ghost"
            className="mb-4"
            onClick={() => setSelectedVideoOption(null)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <p className="text-sm text-muted-foreground">Option not implemented yet.</p>
        </div>
      );
  }
};