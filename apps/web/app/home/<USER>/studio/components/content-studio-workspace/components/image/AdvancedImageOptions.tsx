import React from 'react';
import { Label } from "@kit/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { ImageGenerationOptions } from '../../context/types';
import { useImageContent } from '../../ContentStudioContext';

export const AdvancedImageOptions: React.FC = () => {
  // Get image options directly from context
  const { imageOptions, setImageOptions } = useImageContent();

  return (
    <div className="grid grid-cols-2 gap-4">                
      <div className="space-y-2">
        <Label>Style</Label>
        <Select value={imageOptions.image_styles} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_styles: value as ImageGenerationOptions['image_styles'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select style" />
          </SelectTrigger>
          <SelectContent>
            {["Realism / Photorealism", "Surrealism", "Abstract", "Minimalism", "Impressionism", 
              "Expressionism", "Pop Art", "Vaporwave", "Cyberpunk", "Fantasy", "Anime/Manga", 
              "Watercolor Painting", "Oil Painting", "Sketch", "Cartoon"].map((style) => (
              <SelectItem key={style} value={style}>{style}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label>Mode</Label>
        <Select value={imageOptions.image_modes} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_modes: value as ImageGenerationOptions['image_modes'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select mode" />
          </SelectTrigger>
          <SelectContent>
            {["Photo Mode / Photorealistic", "Digital Art / Illustration", "Concept Art", 
              "3D Render", "Cinematic", "HDR (High Dynamic Range)", "Low-poly"].map((mode) => (
              <SelectItem key={mode} value={mode}>{mode}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Lighting</Label>
        <Select value={imageOptions.image_lighting} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_lighting: value as ImageGenerationOptions['image_lighting'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select lighting" />
          </SelectTrigger>
          <SelectContent>
            {["Natural Light", "Ambient Light", "Soft Light", "Harsh Light", "Backlit", 
              "Side-lit", "Rim Lighting", "Studio Lighting", "Spotlight", "Neon Lighting", 
              "Golden Hour", "Twilight", "Moonlight"].map((lighting) => (
              <SelectItem key={lighting} value={lighting}>{lighting}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label>Detail Level</Label>
        <Select value={imageOptions.image_details} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_details: value as ImageGenerationOptions['image_details'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select detail level" />
          </SelectTrigger>
          <SelectContent>
            {["Ultra-detailed", "Highly detailed", "Intricate", "Minimal Detail", "Simplified", 
              "Hyperrealistic", "Textured", "Smooth", "Grainy", "Photorealistic Details"].map((detail) => (
              <SelectItem key={detail} value={detail}>{detail}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Color Balance</Label>
        <Select value={imageOptions.image_color_balance} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_color_balance: value as ImageGenerationOptions['image_color_balance'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select color balance" />
          </SelectTrigger>
          <SelectContent>
            {["Warm", "Cool", "Neutral", "Vibrant", "Muted", "Pastel", "High Contrast", 
              "Low Contrast", "Monochromatic", "Complementary", "Analogous", "Split-Complementary"].map((balance) => (
              <SelectItem key={balance} value={balance}>{balance}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label>Camera Angle</Label>
        <Select value={imageOptions.image_camera_angle_perspective} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_camera_angle_perspective: value as ImageGenerationOptions['image_camera_angle_perspective'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select camera angle" />
          </SelectTrigger>
          <SelectContent>
            {["Eye-level", "Bird's-eye view", "Worm's-eye view", "Macro", 
              "Wide-angle", "Close-up"].map((angle) => (
              <SelectItem key={angle} value={angle}>{angle}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Composition</Label>
        <Select value={imageOptions.image_composition_framing} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_composition_framing: value as ImageGenerationOptions['image_composition_framing'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select composition" />
          </SelectTrigger>
          <SelectContent>
            {["Rule of Thirds", "Golden Ratio", "Centered", "Asymmetrical", 
              "Dynamic framing"].map((comp) => (
              <SelectItem key={comp} value={comp}>{comp}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label>Environment</Label>
        <Select value={imageOptions.image_environment_setting} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_environment_setting: value as ImageGenerationOptions['image_environment_setting'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select environment" />
          </SelectTrigger>
          <SelectContent>
            {["Indoor", "Outdoor", "Urban", "Rural", "Futuristic", "Natural", 
              "Fantasy", "Industrial"].map((env) => (
              <SelectItem key={env} value={env}>{env}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Mood</Label>
        <Select value={imageOptions.image_mood_atmosphere} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_mood_atmosphere: value as ImageGenerationOptions['image_mood_atmosphere'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select mood" />
          </SelectTrigger>
          <SelectContent>
            {["Calm", "Energetic", "Mysterious", "Whimsical", "Dramatic", "Serene", 
              "Nostalgic"].map((mood) => (
              <SelectItem key={mood} value={mood}>{mood}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Label>Special Effects</Label>
        <Select value={imageOptions.image_special_effects_post_processing} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_special_effects_post_processing: value as ImageGenerationOptions['image_special_effects_post_processing'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select special effects" />
          </SelectTrigger>
          <SelectContent>
            {["Bokeh", "Depth of Field", "Motion Blur", "HDR", 
              "Film Grain", "Vignette", "Color Grading"].map((effect) => (
              <SelectItem key={effect} value={effect}>{effect}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Texture</Label>
        <Select value={imageOptions.image_material_surface_texture} onValueChange={(value) => setImageOptions(prev => ({ ...prev, image_material_surface_texture: value as ImageGenerationOptions['image_material_surface_texture'] }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select texture" />
          </SelectTrigger>
          <SelectContent>
            {["Matte", "Glossy", "Metallic", "Rough", "Smooth", "Reflective", 
              "Textured"].map((texture) => (
              <SelectItem key={texture} value={texture}>{texture}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};