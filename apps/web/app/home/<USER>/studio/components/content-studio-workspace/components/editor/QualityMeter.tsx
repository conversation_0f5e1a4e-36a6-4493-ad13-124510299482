import React from 'react';
import { AlertCircle } from "lucide-react";
import { Badge } from "@kit/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@kit/ui/popover";
import { Card } from "@kit/ui/card";
import { cn } from "@kit/ui/utils";

export interface QualityMeterProps {
  score: number;
  suggestions: string[];
}

export const QualityMeter: React.FC<QualityMeterProps> = ({ score, suggestions }) => {
  const getQualityColor = (score: number) => {
    if (score >= 80) return "bg-green-500/10 text-green-500 hover:bg-green-500/20";
    if (score >= 60) return "bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20";
    return "bg-red-500/10 text-red-500 hover:bg-red-500/20";
  };

  const getQualityText = (score: number) => {
    if (score >= 80) return "High Quality";
    if (score >= 60) return "Medium Quality";
    return "Needs Improvement";
  };

  return (
    <div className="absolute top-4 right-4 z-10">
      <Popover>
        <PopoverTrigger asChild>
          <Badge
            variant="outline"
            className={cn(
              "cursor-pointer transition-colors",
              getQualityColor(score)
            )}
          >
            <AlertCircle className="h-4 w-4 mr-2" />
            {getQualityText(score)} ({score}%)
          </Badge>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <Card className="p-4">
            <h4 className="font-semibold mb-2">How to improve:</h4>
            <ul className="list-disc pl-4 space-y-1">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="text-sm text-muted-foreground">
                  {suggestion}
                </li>
              ))}
            </ul>
          </Card>
        </PopoverContent>
      </Popover>
    </div>
  );
};