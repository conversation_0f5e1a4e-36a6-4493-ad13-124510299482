import React from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@kit/ui/tabs";
import { Loader2 } from "lucide-react";
import { VisualDescriptionInput } from './VisualDescriptionInput';
import { AdvancedImageOptions } from './AdvancedImageOptions';
import { ImageGallery } from './ImageGallery';
import { useImageContent } from '../../ContentStudioContext';

interface AIImageGeneratorProps {
  onGetVisualDescription: () => Promise<any>;
  onSubmit: () => void;
  isGenerating: boolean;
  isGeneratingDescription: boolean;
}

export const AIImageGenerator: React.FC<AIImageGeneratorProps> = ({
  onGetVisualDescription,
  onSubmit,
  isGenerating
}) => {
  // Get state from context
  const {
    input,
    imageUrls,
  } = useImageContent();

  return (
    <div className="space-y-4">
      <Tabs defaultValue="basic" className="w-full">
        {/* <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList> */}

        <TabsContent value="basic" className="space-y-4">
          <VisualDescriptionInput 
            // onGetVisualDescription={onGetVisualDescription}
            // isGeneratingDescription={isGeneratingDescription}
          />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <VisualDescriptionInput 
            // onGetVisualDescription={onGetVisualDescription}
            // isGeneratingDescription={isGeneratingDescription}
          />
          <AdvancedImageOptions />
        </TabsContent>
      </Tabs>

      <Button 
        className="w-full" 
        onClick={onSubmit}
        disabled={isGenerating || !input.trim()}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          'Generate Images'
        )}
      </Button>

      {imageUrls.length > 0 && (
        <ImageGallery 
          imageUrls={imageUrls}
        />
      )}
    </div>
  );
};