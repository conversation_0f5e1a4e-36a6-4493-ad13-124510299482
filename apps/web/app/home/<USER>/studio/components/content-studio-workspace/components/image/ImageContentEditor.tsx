import React from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { ImageSubmenu } from './ImageSubmenu';
import { AIImageGenerator } from './AIImageGenerator';
import { StockImageSelector } from './StockImageSelector';
import { ExistingImageSelector } from './ExistingImageSelector';
import { ImageUploader } from './ImageUploader';
import { SelectedImageEditor } from './SelectedImageEditor';
import { useImageContent } from '../../context/ContentStudioContext';

interface ImageContentEditorProps {
  onGetVisualDescription: () => Promise<any>;
  onSubmit: () => void;
  isGenerating: boolean;
  isGeneratingDescription: boolean;
}

export const ImageContentEditor: React.FC<ImageContentEditorProps> = ({
  onGetVisualDescription,
  onSubmit,
  isGenerating,
  isGeneratingDescription
}) => {
  // Get all state from context
  const {
    selectedImageOption,
    setSelectedImageOption,
  } = useImageContent();
  
  // If no option is selected, show the submenu
  if (selectedImageOption === null) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Select Image Source</h2>
        <ImageSubmenu />
      </div>
    );
  }
  
  // Show the selected option's component
  switch (selectedImageOption) {
    case 'ai':
      return (
        <AIImageGenerator
          onSubmit={onSubmit}
          onGetVisualDescription={onGetVisualDescription}
          isGenerating={isGenerating}
          isGeneratingDescription={isGeneratingDescription}
        />
      );
    case 'stock':
      return <StockImageSelector />;
    case 'upload':
      return <ImageUploader />;
    case 'existing':
      return <ExistingImageSelector  />;
    case 'selected':
      return (
        <SelectedImageEditor />
      );
    default:
      return (
        <div className="space-y-4">
          <Button
            variant="ghost"
            className="mb-4"
            onClick={() => {console.log("clicked,  ", selectedImageOption); setSelectedImageOption(null);}}
          >
            Back
          </Button>
          <p>Option not implemented yet.</p>
        </div>
      );
  }
};