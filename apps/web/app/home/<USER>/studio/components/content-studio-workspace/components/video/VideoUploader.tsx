import React, { useState } from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { Input } from "@kit/ui/input";
import { Upload, ArrowLeft } from "lucide-react";

import { uploadVideoFile } from '../../../../../../../services/storage';
import { useVideoContent } from '../../ContentStudioContext';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

export const VideoUploader: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { setSelectedVideoOption, setSelectedEditorVideo } = useVideoContent();
  const workspace = useTeamAccountWorkspace();
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    
    try {
      const uploadedFile = await uploadVideoFile(selectedFile, workspace.account.id);
      setSelectedEditorVideo(uploadedFile.url); 
      setSelectedVideoOption('selected');
    } catch (error) {
      console.error('Error uploading video:', error);
      // Optionally: Show an error message to the user
    } finally {
      setIsUploading(false);
    }
  };
  
  return (
    <div className="space-y-4">
      <Button 
        variant="ghost" 
        className="mb-4" 
        onClick={() => setSelectedVideoOption('existing')}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
      
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Upload New Video</h3>
        <p className="text-sm text-muted-foreground">
          Select a video file from your computer to upload.
        </p>
      </div>
      
      <div className="border-2 border-dashed rounded-md p-6 text-center">
        <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground mb-4">
          Drag and drop a video file here, or click to browse
        </p>
        
        <Input
          type="file"
          accept="video/*"
          onChange={handleFileChange}
          className="mx-auto max-w-xs"
        />
        
        {selectedFile && (
          <div className="mt-4 text-sm">
            Selected: {selectedFile.name} ({Math.round(selectedFile.size / 1024 / 1024 * 10) / 10} MB)
          </div>
        )}
      </div>
      
      <Button 
        onClick={handleUpload} 
        disabled={!selectedFile || isUploading}
        className="w-full"
      >
        {isUploading ? 'Uploading...' : 'Upload Video'}
      </Button>
    </div>
  );
}; 