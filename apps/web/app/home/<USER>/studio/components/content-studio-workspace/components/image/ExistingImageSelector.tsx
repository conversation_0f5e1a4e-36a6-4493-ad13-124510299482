import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { Input } from "@kit/ui/input";
import { Folder, ArrowLeft } from "lucide-react";
import { ImageGallery } from './ImageGallery';
import { listAssetFolders, listFolderContents } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AssetFolder, StorageFile } from '~/types/assets';

export const ExistingImageSelector: React.FC = () => {
  const { account } = useTeamAccountWorkspace();
  const [folders, setFolders] = useState<AssetFolder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [folderImages, setFolderImages] = useState<StorageFile[]>([]);
  const [filterQuery, setFilterQuery] = useState('');

  // Fetch folders on mount
  useEffect(() => {
    const fetchFolders = async () => {
      if (account?.id) {
        const assetFolders = await listAssetFolders(account.id);
        setFolders(assetFolders);
      }
    };
    fetchFolders();
  }, [account?.id]);

  // Fetch folder contents when a folder is selected
  useEffect(() => {
    const fetchFolderContents = async () => {
      if (account?.id && selectedFolder) {
        const contents = await listFolderContents(selectedFolder, account.id);
        // Filter only image files
        const imageFiles = contents.filter(file => file.type?.startsWith('image/'));
        setFolderImages(imageFiles);
      }
    };
    if (selectedFolder) {
      fetchFolderContents();
    }
  }, [account?.id, selectedFolder]);

  const handleFilter = () => {
    // Filter folders by name
    console.log('Filtering with:', filterQuery);
  };

  // If a folder is selected, show its contents
  if (selectedFolder) {
    return (
      <div className="space-y-4">
        <Button 
          variant="ghost" 
          className="mb-4" 
          onClick={() => setSelectedFolder(null)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Folders
        </Button>

        <div className="mb-4">
          <h3 className="text-md font-medium mb-2">Images in {selectedFolder}</h3>
          <p className="text-sm text-muted-foreground">Select an image to use in your content.</p>
        </div>

        {folderImages.length > 0 ? (
          <ImageGallery
            imageUrls={folderImages.map(file => file.url)}
          />
        ) : (
          <div className="h-40 bg-muted rounded-md flex items-center justify-center">
            <p className="text-sm text-muted-foreground">No images found in this folder</p>
          </div>
        )}
      </div>
    );
  }

  // Show folder list
  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Browse Asset Folders</h3>
        <p className="text-sm text-muted-foreground">Select a folder to view its images.</p>
      </div>
      
      <div className="flex gap-2">
        <Input 
          placeholder="Filter folders..."
          className="flex-1"
          value={filterQuery}
          onChange={(e) => setFilterQuery(e.target.value)}
        />
        <Button onClick={handleFilter}>Filter</Button>
      </div>
      
      {folders.length > 0 ? (
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
          {folders.map((folder) => (
            <div
              key={folder.name}
              onClick={() => setSelectedFolder(folder.name)}
              className="flex cursor-pointer flex-col items-center rounded-lg border p-4 hover:bg-gray-50"
            >
              <Folder className="h-12 w-12 text-blue-500" />
              <span className="mt-2 text-sm">{folder.name}</span>
            </div>
          ))}
        </div>
      ) : (
        <div className="h-40 bg-muted rounded-md flex items-center justify-center">
          <p className="text-sm text-muted-foreground">No asset folders found</p>
        </div>
      )}
    </div>
  );
};