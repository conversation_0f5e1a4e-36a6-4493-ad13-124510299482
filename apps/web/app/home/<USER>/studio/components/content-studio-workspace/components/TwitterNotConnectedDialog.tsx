'use client';

import { But<PERSON> } from "@kit/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@kit/ui/dialog";
import { useRouter } from "next/navigation";
import { Twitter } from "lucide-react";

interface TwitterNotConnectedDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountSlug: string;
}

export function TwitterNotConnectedDialog({
  open,
  onOpenChange,
  accountSlug,
}: TwitterNotConnectedDialogProps) {
  const router = useRouter();

  const handleGoToIntegrations = () => {
    router.push(`/home/<USER>/integrations`);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Twitter className="h-5 w-5 text-[#1DA1F2]" />
            Twitter/X Connection Required
          </DialogTitle>
          <DialogDescription>
            You need to connect your Twitter/X account to use this feature.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            Connect your Twitter/X account to share updates, view your profile information, and engage with your audience.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleGoToIntegrations}>
            Go to Integrations
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 