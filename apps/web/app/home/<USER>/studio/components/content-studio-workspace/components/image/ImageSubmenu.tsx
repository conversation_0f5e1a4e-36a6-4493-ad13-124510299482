import React from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { Image, ImageDown, Sparkles } from "lucide-react";
import { useImageContent } from '../../ContentStudioContext';

export const ImageSubmenu: React.FC = () => {
  // Get the setSelectedImageOption directly from context
  const { setSelectedImageOption } = useImageContent();

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-6">Image Options</h3>
      <div className="flex flex-wrap gap-2 ">
        <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[calc(50%-4px)] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedImageOption('ai')}
        >
          <Sparkles className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Generate AI Image</span>
        </Button>
        <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[calc(50%-4px)] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedImageOption('stock')}
        >
          <Image className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Use Stock Image</span>
        </Button>
        <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[calc(50%-4px)] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedImageOption('existing')}
        >
          <ImageDown className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Use Image from Asset Library</span>
        </Button>
        {/* <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[calc(50%-4px)] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedImageOption('upload')}
        >
          <ImageUp className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Upload Image</span>
        </Button> */}
      </div>
    </div>
  );
};