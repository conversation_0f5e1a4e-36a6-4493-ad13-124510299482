import React, { useEffect, useState } from 'react';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "~/components/resizable/resizable";
import { CompanyContent } from "~/types/company-content";
import { ContentTypeSelector } from './ContentTypeSelector';
import { SubMenu } from './SubMenu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { useBaseContent, useImageContent, useVideoContent } from './ContentStudioContext';
import { BlogPostPreview, ContentEditor } from './components/editor';
import { LinkedInPreview } from './components/editor/LinkedInPreview';
import { TwitterPreview } from './components/editor/TwitterPreview';
import { VideoContentEditor } from './components/video/VideoContentEditor';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { useParams, useRouter } from 'next/navigation';
import { X } from 'lucide-react';
import Link from 'next/link';

interface SocialProfile {
  id: string;
  platform: string;
  username: string | null;
  user_image?: string | null;
  profile_url: string | null;
  display_name: string | null;
  headline?: string | null;
  is_connected: boolean | null;
  profile_key?: string; // Add profile_key for Ayrshare API calls
}

/**
 * Main workspace component for the Content Studio
 */
export default function ContentStudioWorkspace() {
  const [selectedProfile, setSelectedProfile] = useState<SocialProfile | null>(null);
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const router = useRouter();
  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", params.account as string),
    {
      ttl: '10m'
    }
  );

  const [socialAccounts] = useZeroQuery(
    zero.query.ayrshare_social_profiles
    .where("company_id", "=", params.account as string),
    {
      ttl: '10m'
    }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];

  const { 
    selectedType, 
    setSelectedType, 
  } = useBaseContent();
  
  const {
    setSelectedEditorImage,
    setSelectedImageOption
  } = useImageContent();

  const {
    selectedVideoOption
  } = useVideoContent();


  console.log("selectedCompanyContent", selectedCompanyContent);
  return (
    <div className="h-[calc(100vh-4rem)] w-full">
      <ResizablePanelGroup direction="horizontal" className="h-full w-full">
        {!(selectedVideoOption === 'create') 
        && 
        <ResizablePanel
          defaultSize={20}
          minSize={15} 
          className="bg-background border-r pt-10 overflow-hidden">

            {selectedType === null ? (
              <ContentTypeSelector />
            ) : (
              <SubMenu onBack={() => {
                setSelectedType(null);
                setSelectedEditorImage(null);
                setSelectedImageOption(null);
              }} />
            )}
        </ResizablePanel>}
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={80} className="flex flex-col">
             {/* Conditionally show social profile selector and post button only for social media channels */}
             {(selectedCompanyContent?.channel === "LinkedIn" || 
               selectedCompanyContent?.channel === "Twitter" || 
               selectedCompanyContent?.channel === "X" || 
               selectedCompanyContent?.channel === "Tweet") && (
               <div className="flex justify-between border-b py-4 w-full">
                 <div className="w-[200px] ml-12">
                   <SocialProfilesSelection 
                     channel={selectedCompanyContent?.channel}
                     socialAccounts={socialAccounts}
                     selectedProfile={selectedProfile}
                     onProfileChange={setSelectedProfile}
                   />
                 </div>
                 
                     {/* Add a close button that takes the user back to the tasks page */}
                     <Button className="mr-12" variant="ghost" onClick={() => {
                        router.back();
                     }}>
                      <X className="h-4 w-4" />
                     </Button>
               </div>
             )}
               <div className="overflow-y-auto flex-1">
                {(() => {   
                  if(!selectedCompanyContent) return null;               
                  switch (selectedCompanyContent?.channel) {
                    case "Blog":
                      return <BlogPostPreview selectedProfile={selectedProfile} />;
                    case "LinkedIn":
                      return <LinkedInPreview selectedProfile={selectedProfile} />;
                    case "Twitter":
                    case "X":
                    case "Tweet":  
                      return <TwitterPreview selectedProfile={selectedProfile} />;
                    default:
                      return <BlogPostPreview selectedProfile={selectedProfile} />;
                  }
                })()}
              </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

// Export main components and context for use elsewhere
export * from './ContentStudioContext';
export * from './components';

// Export the SocialProfile interface for use by Preview components
export type { SocialProfile };

interface SocialProfilesSelectionProps {
  channel?: string;
  socialAccounts?: any[];
  selectedProfile: SocialProfile | null;
  onProfileChange: (profile: SocialProfile | null) => void;
}

function SocialProfilesSelection({ channel, socialAccounts, selectedProfile, onProfileChange }: SocialProfilesSelectionProps) {
  const workspace = useTeamAccountWorkspace();
  
  // Get available profiles for the current channel
  const getAvailableProfiles = (): SocialProfile[] => {
    if (!channel || !socialAccounts?.length) return [];

    const channelPlatform = channel.toLowerCase();
    const platformMap: Record<string, string> = {
      'linkedin': 'linkedin',
      'twitter': 'twitter',
      'x': 'twitter',
      'tweet': 'twitter'
    };

    const targetPlatform = platformMap[channelPlatform];
    if (!targetPlatform) return [];

    // Filter connected accounts for the target platform
    const connectedProfiles = socialAccounts
      .filter(account => 
        account.platform === targetPlatform && 
        account.is_connected === true &&
        account.company_id === workspace.account.id
      )
      .map(account => ({
        id: account.id,
        platform: account.platform,
        username: account.username,
        user_image: account.user_image,
        profile_url: account.profile_url,
        display_name: account.display_name,
        headline: account.headline,
        is_connected: account.is_connected,
        profile_key: account.profile_key, // Include profile_key
        ...account
      }));

    console.log("connectedProfiles", connectedProfiles, socialAccounts);
    return connectedProfiles;
  };

  const availableProfiles = getAvailableProfiles();

  // Auto-select the first profile if no profile is selected and profiles are available
  useEffect(() => {
    if (availableProfiles.length > 0 && !selectedProfile) {
      const firstProfile = availableProfiles[0];
      if (firstProfile) {
        onProfileChange(firstProfile);
      }
    }
  }, [availableProfiles, selectedProfile, onProfileChange]);

  // Show connect link if no profiles available
  if (!availableProfiles.length) {
    return (
      <div className="text-center">
        <a 
          href={`/home/<USER>/integrations/socials`}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          Connect Social Account in Settings
        </a>
      </div>
    );
  }

  const handleProfileChange = (profileId: string) => {
    const profile = availableProfiles.find(p => p.id === profileId) || null;
    onProfileChange(profile);
  };

  return (
    <Select value={selectedProfile?.id || ''} onValueChange={handleProfileChange}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select account" />
      </SelectTrigger>
      <SelectContent>
        {availableProfiles.map((profile) => (
          <SelectItem key={profile.id} value={profile.id}>
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={profile.user_image || undefined} alt={profile.display_name || 'Profile'} />
                <AvatarFallback>
                  {profile.display_name?.charAt(0).toUpperCase() || 'P'}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">{profile.display_name || 'Unknown Profile'}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}