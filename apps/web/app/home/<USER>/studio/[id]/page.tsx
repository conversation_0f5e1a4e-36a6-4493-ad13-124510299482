'use client'
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ContentStudioProvider } from '../components/content-studio-workspace/';
import ContentStudioWorkspace from '../components/content-studio-workspace';

export default function TaskPage() {

    return (
      <DndProvider backend={HTML5Backend}>
          <ContentStudioProvider>
            <div className="space-y-6 flex flex-row relative">
              {/* <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 z-10"
                onClick={() => router.back()}
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Close</span>
              </Button> */}
                <ContentStudioWorkspace />
            </div>
          </ContentStudioProvider>
      </DndProvider>
    );
}
