'use client';

import { useRouter } from 'next/navigation';
import { TrialStatusCard } from '~/components/trial-status-card';
import { useTrialStatus } from '~/hooks/use-trial-status';

interface TrialBillingSectionProps {
  accountId: string;
  accountSlug: string;
}

export function TrialBillingSection({ accountId, accountSlug }: TrialBillingSectionProps) {
  const router = useRouter();
  const { trialInfo, isLoading, startTrial } = useTrialStatus({ accountId });

  const handleStartTrial = async () => {
    try {
      const result = await startTrial(7);
      if (!result.success) {
        console.error('Failed to start trial:', result.error);
      }
    } catch (error) {
      console.error('Failed to start trial:', error);
    }
  };

  const handleUpgrade = () => {
    // Scroll to checkout section or redirect to upgrade flow
    const checkoutElement = document.getElementById('checkout-section');
    if (checkoutElement) {
      checkoutElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (isLoading || !trialInfo) {
    return null;
  }

  // Only show trial card for accounts that have trial functionality
  if (trialInfo.status === 'converted') {
    return null; // Don't show for already converted accounts
  }

  return (
    <div className="mb-6">
      <TrialStatusCard
        trialInfo={trialInfo}
        onUpgrade={handleUpgrade}
        onStartTrial={trialInfo.status === 'inactive' ? handleStartTrial : undefined}
      />
    </div>
  );
}
