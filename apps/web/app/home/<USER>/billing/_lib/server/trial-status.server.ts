'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

export interface TrialStatusData {
  accountId: string;
  status: 'inactive' | 'active' | 'expired' | 'converted';
  startedAt: string | null;
  endsAt: string | null;
  daysRemaining: number | null;
  isExpired: boolean;
  isActive: boolean;
  isConverted: boolean;
  isInactive: boolean;
  isPaid: boolean;
  progressPercentage: number;
  timeRemaining: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

export async function getTrialStatusFromSubscriptions(accountId: string): Promise<TrialStatusData | null> {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  try {
    logger.info({ accountId }, 'Getting trial status from subscriptions');

    // Use the database helper functions to get trial status
    const { data: trialStatus, error: statusError } = await client
      .rpc('get_account_trial_status', { account_id: accountId });

    if (statusError) {
      logger.error({ error: statusError, accountId }, 'Failed to get trial status');
      return null;
    }

    // Get trial start date
    const { data: trialStartedAt, error: startError } = await client
      .rpc('get_account_trial_started_at', { account_id: accountId });

    if (startError) {
      logger.error({ error: startError, accountId }, 'Failed to get trial start date');
    }

    // Get trial end date
    const { data: trialEndsAt, error: endError } = await client
      .rpc('get_account_trial_ends_at', { account_id: accountId });

    if (endError) {
      logger.error({ error: endError, accountId }, 'Failed to get trial end date');
    }

    // Calculate time-based properties
    const now = Date.now();
    let daysRemaining: number | null = null;
    let isExpired = false;
    let progressPercentage = 0;
    let timeRemaining: TrialStatusData['timeRemaining'] = null;

    if (trialEndsAt && trialStartedAt) {
      const startTime = new Date(trialStartedAt).getTime();
      const endTime = new Date(trialEndsAt).getTime();
      const totalDuration = endTime - startTime;
      const elapsed = now - startTime;
      const remaining = endTime - now;

      // Calculate days remaining (can be negative if expired)
      daysRemaining = Math.ceil(remaining / (1000 * 60 * 60 * 24));
      isExpired = remaining <= 0;

      // Calculate progress percentage (0-100)
      progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

      // Calculate detailed time remaining
      if (remaining > 0) {
        const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
        const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));

        timeRemaining = { days, hours, minutes };
      }
    }

    // Override isExpired if status is already expired
    if (trialStatus === 'expired') {
      isExpired = true;
    }

    // Status flags for easy conditional rendering
    const isConverted = trialStatus === 'converted';
    const isInactive = trialStatus === 'inactive';
    const isPaid = isConverted;

    // Determine final status - override if expired
    const finalStatus = isExpired && trialStatus === 'active' ? 'expired' : trialStatus;

    const trialData: TrialStatusData = {
      accountId,
      status: finalStatus,
      startedAt: trialStartedAt,
      endsAt: trialEndsAt,
      daysRemaining,
      isExpired,
      isActive: finalStatus === 'active' && !isExpired,
      isConverted,
      isPaid,
      isInactive,
      progressPercentage,
      timeRemaining,
    };

    logger.info({ accountId, trialStatus: finalStatus }, 'Successfully retrieved trial status from subscriptions');
    return trialData;

  } catch (error) {
    logger.error({ error, accountId }, 'Error getting trial status from subscriptions');
    return null;
  }
}

export async function startTrial(accountId: string, trialDays: number = 7): Promise<{ success: boolean; error?: string }> {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  try {
    logger.info({ accountId, trialDays }, 'Starting trial for account');

    // Create a trial subscription
    const trialStartDate = new Date();
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + trialDays);

    const { data: subscription, error } = await client
      .from('subscriptions')
      .insert({
        account_id: accountId,
        status: 'trialing',
        active: true,
        trial_starts_at: trialStartDate.toISOString(),
        trial_ends_at: trialEndDate.toISOString(),
        period_starts_at: trialStartDate.toISOString(),
        period_ends_at: trialEndDate.toISOString(),
        currency: 'usd',
        cancel_at_period_end: false,
      })
      .select()
      .single();

    if (error) {
      logger.error({ error, accountId }, 'Failed to create trial subscription');
      return { success: false, error: error.message };
    }

    logger.info({ accountId, subscriptionId: subscription.id }, 'Successfully started trial');
    return { success: true };

  } catch (error) {
    logger.error({ error, accountId }, 'Error starting trial');
    return { success: false, error: 'Failed to start trial' };
  }
}

export async function convertTrial(accountId: string): Promise<{ success: boolean; error?: string }> {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  try {
    logger.info({ accountId }, 'Converting trial to paid subscription');

    // Update the trial subscription to active status
    const { error } = await client
      .from('subscriptions')
      .update({
        status: 'active',
        active: true,
      })
      .eq('account_id', accountId)
      .eq('status', 'trialing');

    if (error) {
      logger.error({ error, accountId }, 'Failed to convert trial subscription');
      return { success: false, error: error.message };
    }

    logger.info({ accountId }, 'Successfully converted trial to paid subscription');
    return { success: true };

  } catch (error) {
    logger.error({ error, accountId }, 'Error converting trial');
    return { success: false, error: 'Failed to convert trial' };
  }
} 