'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { revalidatePath } from 'next/cache';
import { getLinkedInProfile, disconnectLinkedIn } from '../../../../../services/linkedin';
import { getLogger } from '@kit/shared/logger';
import { getUniqueId } from '~/services/utils';

/**
 * Interface for LinkedIn OAuth data
 */
interface LinkedInOAuthData {
  state: string;
  access_token: string;
  expires_in?: number;
  refresh_token?: string;
  refresh_token_expires_in?: number;
  scope?: string;
  user_id: string;
  first_name?: string;
  last_name?: string;
  headline?: string;
  profile_picture_url?: string;
}

/**
 * Server action to store LinkedIn OAuth data in the database
 * This is used by the LinkedIn OAuth callback handler
 * 
 * @param data LinkedIn OAuth data to store
 * @returns Result of the operation
 */
export async function storeLinkedInData(data: LinkedInOAuthData) {
  try {
    const { 
      state, 
      access_token, 
      expires_in, 
      refresh_token, 
      refresh_token_expires_in, 
      scope,
      user_id,
      first_name,
      last_name,
      headline,
      profile_picture_url
    } = data;

    // Validate required parameters
    if (!state || !access_token || !user_id) {
      return {
        success: false,
        error: 'Missing required parameters'
      };
    }

    // Get the Supabase client
    const supabase = getSupabaseServerClient();

    try {
      // Using upsert to handle both new connections and updates

      const { error: queryError } = await supabase
        .from('linkedInState')
        .upsert({
          user_id,
          state, 
          access_token, 
          expires_in: expires_in ? new Date(Date.now() + expires_in * 1000).toISOString() : null, 
          refresh_token: refresh_token || null, 
          refresh_token_expires_in: refresh_token_expires_in 
            ? new Date(Date.now() + refresh_token_expires_in * 1000).toISOString() 
            : null, 
          scope: scope || null,
          first_name: first_name || null,
          last_name: last_name || null,
          headline: headline || null,
          profile_picture_url: profile_picture_url || null
        }, {
          onConflict: 'user_id'
        });
      
      if (queryError) {
        throw queryError;
      }
      
      // Revalidate the integrations page to show updated connection state
      revalidatePath('/home/<USER>/integrations');
      
      return { success: true };
    } catch (sqlError) {
      console.error('Error storing LinkedIn data:', sqlError);
      return {
        success: false,
        error: 'Database error',
        details: (sqlError as Error).message
      };
    }
  } catch (error) {
    console.error('Error processing LinkedIn data:', error);
    return {
      success: false,
      error: 'Internal server error'
    };
  }
}

/**
 * Get LinkedIn profile for the current user
 * 
 * @param userId User ID to get profile for
 * @returns LinkedIn profile data or null
 */
export async function getUserLinkedInProfile(userId: string) {
  try {
    const profile = await getLinkedInProfile(userId);
    
    return {
      success: true,
      profile
    };
  } catch (error) {
    console.error('Error fetching LinkedIn profile:', error);
    return {
      success: false,
      error: 'Failed to fetch LinkedIn profile'
    };
  }
}

/**
 * Disconnect LinkedIn for a user
 * 
 * @param userId User ID to disconnect
 * @returns Success or failure
 */
export async function disconnectUserLinkedIn(userId: string) {
  try {
    const success = await disconnectLinkedIn(userId);
    
    if (success) {
      // Revalidate the integrations page to show updated connection state
      revalidatePath('/home/<USER>/integrations');
      
      return {
        success: true
      };
    }
    
    return {
      success: false,
      error: 'Failed to disconnect LinkedIn'
    };
  } catch (error) {
    console.error('Error disconnecting LinkedIn:', error);
    return {
      success: false,
      error: 'Internal server error'
    };
  }
}

/**
 * Create a new social profile using Ayrshare API
 */
export const createProfile = async (userId: string, companyId: string, title?: string) => {
  const logger = await getLogger();
  
  try { 
    logger.info('Creating social profile', { userId, companyId, title });

    const response = await fetch("https://api.ayrshare.com/api/profiles", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`
      },
      body: JSON.stringify({
        title: getUniqueId()
      }),
    });
    
    const responseData = await response.json();
    logger.info('Ayrshare API response', { 
      status: responseData.status, 
      profileKey: responseData.profileKey,
      refId: responseData.refId 
    });

    if (responseData.status === 'success') {
      // Return the data from our database which includes the profileKey
      return {
        status: 'success',
        profileKey: responseData.profileKey,
        refId: responseData.refId,
        title: responseData.title,
        profileName:  title,
        messagingActive: responseData.messagingActive
      };
    } else {
      console.error('Ayrshare API error', { responseData });
      logger.error('Ayrshare API error', { responseData });
      return {
        status: 'error',
        error: 'Failed to create profile with Ayrshare API'
      };
    }
  } catch (error) {
    logger.error('Error creating profile', { error });
    return {
      status: 'error',
      error: 'Internal server error'
    };
  }
};

export const generateJWT = async (profileKey: string) => {
  console.log("profileKey", {profileKey});
  const decodedPrivateKey = Buffer.from(process.env.AYRSHARE_PRIVATE_KEY || '', 'base64').toString('utf-8');
  const response = await fetch("https://api.ayrshare.com/api/profiles/generateJWT", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`
    },
    body: JSON.stringify({
      domain: process.env.AYRSHARE_DOMAIN,
      privateKey: decodedPrivateKey,
      profileKey,
      logout: true
    }),
  });
  const data = await response.json();
  return data;
};

export const getSocialProfiles = async (userId: string, companyId: string) => {
  const logger = await getLogger();
  
  try {
    console.log('Fetching social profiles', { userId, companyId });

    const supabase = getSupabaseServerClient();
    const { data, error } = await (supabase as any)
      .from('ayrshare_user_profile')
      .select('*')
      // .eq('user_id', userId)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    console.log("data", {data});
    if (error) {
      logger.error('Database error fetching social profiles', { error });
      throw error;
    }

    logger.info('Social profiles fetched successfully', { count: data?.length || 0 });
    return data || [];
  } catch (error) {
    logger.error('Error fetching social profiles', { error });
    return [];
  }
};

export const getProfilesDetails = async (profileKey: string) => {
  const logger = await getLogger();
  
  try {
    logger.info('Fetching profile details', { profileKey });

    const displayInfoRaw = await fetch(`https://api.ayrshare.com/api/user`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`,
        "Profile-Key": profileKey
      },
    });
    
    const displayInfo = await displayInfoRaw.json();
    console.log("displayInfo", {displayInfo});
    if (displayInfo.activeSocialAccounts) {
      logger.info('Profile details fetched successfully', { 
        accountCount: displayInfo.activeSocialAccounts?.length || 0 
      });
      return displayInfo;
    } else {
      logger.warn('No active social accounts found');
      return {};
    }
  } catch (error) {
    logger.error('Error fetching profile details', { error });
    return {};
  }
};

export const disconnectSocialProfile = async (platform: string, profileKey: string) => {
  const logger = await getLogger();
  
  try {
    logger.info('Disconnecting social profile', { platform, profileKey });

    const response = await fetch('https://api.ayrshare.com/api/profiles/social', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey,
      },
      body: JSON.stringify({
        platform: platform
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to disconnect ${platform}: ${error}`);
    }

    const result = await response.json();
    logger.info('Social profile disconnected successfully', { platform });
    
    return result;
  } catch (error) {
    logger.error('Error disconnecting social profile', { error });
    throw error;
  }
};

/**
 * Delete a social profile
 */
export const deleteSocialProfile = async (profileId: string) => {
  const logger = await getLogger();
  
  try {
    logger.info('Deleting social profile', { profileId });

      logger.info('Disconnecting social profile', { profileId });
  
      const response = await fetch('https://api.ayrshare.com/api/profiles', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
          'Profile-Key': profileId,
        }
      });
  
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Failed to delete profile ${error}`);
      }
  
      const result = await response.json();
      logger.info('Social profile disconnected successfully', { profileId });

      return result;
  } catch (error) {
    console.log('Error deleting social profile', { error });
    return {
      success: false,
      error: 'Failed to delete profile'
    };
  }
};

/**
 * Register a webhook for a specific profile
 */
export const registerWebhook = async (profileKey: string, action: string = 'social') => {
  const logger = await getLogger();
  
  try {
    logger.info('Registering webhook', { profileKey, action });

    // Get the base URL from environment variables
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const webhookUrl = `${baseUrl}/api/integrations/ayrshare/webhook`;
    // const webhookUrl = `https://webhook.site/44d131b4-9ccc-47e5-bc9c-b5259b93bbb8`;
    
    // Get the webhook secret from environment variables
    const webhookSecret = process.env.AYRSHARE_WEBHOOK_SECRET;

    const payload: any = {
      action: action,
      url: webhookUrl,
    };

    // Add secret if configured
    if (webhookSecret) {
      payload.secret = webhookSecret;
    }

    const response = await fetch('https://api.ayrshare.com/api/hook/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey,
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok) {
      logger.error('Failed to register webhook', { 
        profileKey, 
        action, 
        status: response.status,
        error: responseData 
      });
      return {
        status: 'error',
        error: 'Failed to register webhook',
        details: responseData
      };
    }

    logger.info('Successfully registered webhook', { 
      profileKey, 
      action, 
      webhookUrl,
      refId: responseData.refId 
    });

    return {
      status: 'success',
      data: responseData
    };
  } catch (error) {
    logger.error('Error registering webhook', { error });
    return {
      status: 'error',
      error: 'Internal server error'
    };
  }
};

/**
 * Unregister a webhook for a specific profile
 */
export const unregisterWebhook = async (profileKey: string, action: string = 'social') => {
  const logger = await getLogger();
  
  try {
    logger.info('Unregistering webhook', { profileKey, action });

    const response = await fetch('https://api.ayrshare.com/api/hook/webhook', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey,
      },
      body: JSON.stringify({
        action: action,
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      logger.error('Failed to unregister webhook', { 
        profileKey, 
        action, 
        status: response.status,
        error: responseData 
      });
      return {
        status: 'error',
        error: 'Failed to unregister webhook',
        details: responseData
      };
    }

    logger.info('Successfully unregistered webhook', { 
      profileKey, 
      action,
      refId: responseData.refId 
    });

    return {
      status: 'success',
      data: responseData
    };
  } catch (error) {
    logger.error('Error unregistering webhook', { error });
    return {
      status: 'error',
      error: 'Internal server error'
    };
  }
};