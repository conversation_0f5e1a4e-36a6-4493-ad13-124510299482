'use server';

import { createCreateTeamAccountService } from 'node_modules/@kit/team-accounts/src/server/services/create-team-account.service';
import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { onboardingSchema } from '../schemas/onboarding';
import { CreateInitialCampaignResponse } from '~/types/Campaign';

import { z } from 'zod';
import { getUniqueId } from '~/services/utils';

export const completeOnboardingAction = enhanceAction(
  async function (
    data: z.infer<typeof onboardingSchema>,
    user: { id: string }
  ) {
    const ctx = {
      name: 'complete-onboarding-action',
      userId: user.id,
    };
    const logger = await getLogger();
    logger.info(ctx, 'Starting onboarding process...');

    const client = getSupabaseServerClient();
    const teamAccountsApi = createCreateTeamAccountService(client);

    let contentData: any;
    // Create initial content via API route
    const contentResult = await fetch(
      `${process.env.NEXT_PUBLIC_PUSH_SERVER}/scrape?url=${data.companyWebsite}`, 
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    try {
      contentData = await contentResult.json();
    } catch {
      contentData = { error: 'Failed to parse response' };
    }
   
    try {
      // Create team account with company name
      const { data: teamAccount, error: teamError } =
        await teamAccountsApi.createNewOrganizationAccount({
          name: data.companyName,
          userId: user.id,
          website: data.companyWebsite,
          public_data: JSON.stringify({
            cleanedText: contentData?.content || '',
          }),
        });

      if (teamError || !teamAccount) {
        logger.error(ctx, 'Failed to create team account', {
          error: teamError,
        });
        throw new Error('Failed to create team account');
      }
      console.log('teamAccount', teamAccount);

      // Store additional onboarding data in metadata
      const { error: metadataError } = await client.auth.updateUser({
        data: {
          onboarded: true,
          defaultAccountId: teamAccount.id,
          companyWebsite: data.companyWebsite
        },
      });

      if (metadataError) {
        logger.error(ctx, 'Failed to update user metadata', {
          error: metadataError,
        });
        throw new Error('Failed to update user metadata');
      }

      console.log('Scraping website content...', {
        company_id: teamAccount.id,
        user_id: user.id,
        websiteUrl: data.companyWebsite,
        company_name: data.companyName,
      });
      
      

   

      logger.info(ctx, 'Successfully completed onboarding');

      return {
        success: true,
        teamAccount,
        initialCampaign: 'campaignId' in contentData ? contentData : undefined
      };
    } catch (error) {
      logger.error(ctx, 'Onboarding process failed', { error });
      throw error;
    }
  },
  {
    auth: true,
    schema: onboardingSchema,
  }
);

export const checkUserInvitations = enhanceAction(
  async function (email: string) {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    console.log('email', email);
    try {
      const { data: invitations, error } = await client
        .from('invitations')
        .select('*')
        .eq('email', email.toLowerCase())
        // .is('accepted', false);
        console.log('invitations', invitations);
      if (error) {
        logger.error({ name: 'check-user-invitations' }, 'Failed to check invitations', { 
          error, 
          email 
        });
        throw error;
      }
      
      return {
        invitations: invitations || [],
        hasInvitations: (invitations || []).length > 0
      };
    } catch (error) {
      logger.error({ name: 'check-user-invitations' }, 'Error checking invitations', { 
        error, 
        email 
      });
      throw error;
    }
  },
  {
    auth: true,
  }
);
