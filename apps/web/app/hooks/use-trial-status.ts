'use client';

import { useMemo, useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getTrialStatusFromSubscriptions, startTrial, convertTrial, type TrialStatusData } from '../home/<USER>/billing/_lib/server/trial-status.server';

// TypeScript interfaces for trial system
export interface TrialInfo {
  accountId: string;
  status: TrialStatus;
  startedAt: number | null;
  endsAt: number | null;
  daysRemaining: number | null;
  isExpired: boolean;
  isActive: boolean;
  isConverted: boolean;
  isInactive: boolean;
  isPaid: boolean;
  progressPercentage: number;
  timeRemaining: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

// Trial status enum type matching database enum
export type TrialStatus = 'inactive' | 'active' | 'expired' | 'converted';

export interface UseTrialStatusOptions {
  accountId: string;
  enabled?: boolean;
}

export interface UseTrialStatusReturn {
  trialInfo: TrialInfo | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  startTrial: (trialDays?: number) => Promise<{ success: boolean; error?: string }>;
  convertTrial: () => Promise<{ success: boolean; error?: string }>;
}

/**
 * Hook to get real-time trial status for an account using subscription data
 * @param options - Configuration options
 * @returns Trial status information with real-time updates
 */
export function useTrialStatus({ 
  accountId, 
  enabled = true 
}: UseTrialStatusOptions): UseTrialStatusReturn {
  const [refetchTrigger, setRefetchTrigger] = useState(0);

  // Use React Query to fetch trial status from server
  const { data: trialData, isLoading, error, refetch } = useQuery({
    queryKey: ['trial-status', accountId, refetchTrigger],
    queryFn: () => getTrialStatusFromSubscriptions(accountId),
    enabled: enabled && !!accountId,
    refetchInterval: 30000, // Refetch every 30 seconds to keep trial status updated
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  // Convert server data to client format
  const trialInfo = useMemo((): TrialInfo | null => {
    if (!trialData) return null;

    return {
      accountId: trialData.accountId,
      status: trialData.status,
      startedAt: trialData.startedAt ? new Date(trialData.startedAt).getTime() : null,
      endsAt: trialData.endsAt ? new Date(trialData.endsAt).getTime() : null,
      daysRemaining: trialData.daysRemaining,
      isExpired: trialData.isExpired,
      isActive: trialData.isActive,
      isConverted: trialData.isConverted,
      isPaid: trialData.isPaid,
      isInactive: trialData.isInactive,
      progressPercentage: trialData.progressPercentage,
      timeRemaining: trialData.timeRemaining,
    };
  }, [trialData]);

  // Trial management functions
  const handleStartTrial = async (trialDays: number = 7) => {
    const result = await startTrial(accountId, trialDays);
    if (result.success) {
      // Trigger a refetch to get updated trial status
      setRefetchTrigger(prev => prev + 1);
    }
    return result;
  };

  const handleConvertTrial = async () => {
    const result = await convertTrial(accountId);
    if (result.success) {
      // Trigger a refetch to get updated trial status
      setRefetchTrigger(prev => prev + 1);
    }
    return result;
  };

  // Auto-refetch when trial is about to expire
  useEffect(() => {
    if (trialInfo?.isActive && trialInfo.daysRemaining !== null && trialInfo.daysRemaining <= 1) {
      // Refetch more frequently when trial is about to expire
      const interval = setInterval(() => {
        setRefetchTrigger(prev => prev + 1);
      }, 60000); // Every minute

      return () => clearInterval(interval);
    }
  }, [trialInfo?.isActive, trialInfo?.daysRemaining]);

  return {
    trialInfo,
    isLoading,
    error: error as Error | null,
    refetch: () => {
      setRefetchTrigger(prev => prev + 1);
    },
    startTrial: handleStartTrial,
    convertTrial: handleConvertTrial,
  };
}

/**
 * Hook to get trial status for multiple accounts
 * @param accountIds - Array of account IDs
 * @returns Map of account ID to trial info
 */
export function useMultipleTrialStatuses(accountIds: string[]) {
  const [refetchTrigger, setRefetchTrigger] = useState(0);

  // Use React Query to fetch trial status for multiple accounts
  const { data: trialDataMap, isLoading, error } = useQuery({
    queryKey: ['multiple-trial-statuses', accountIds, refetchTrigger],
    queryFn: async () => {
      const results = await Promise.all(
        accountIds.map(async (accountId) => {
          const trialData = await getTrialStatusFromSubscriptions(accountId);
          return { accountId, trialData };
        })
      );

      const map = new Map<string, TrialInfo>();
      results.forEach(({ accountId, trialData }) => {
        if (trialData) {
          map.set(accountId, {
            accountId: trialData.accountId,
            status: trialData.status,
            startedAt: trialData.startedAt ? new Date(trialData.startedAt).getTime() : null,
            endsAt: trialData.endsAt ? new Date(trialData.endsAt).getTime() : null,
            daysRemaining: trialData.daysRemaining,
            isExpired: trialData.isExpired,
            isActive: trialData.isActive,
            isConverted: trialData.isConverted,
            isPaid: trialData.isPaid,
            isInactive: trialData.isInactive,
            progressPercentage: trialData.progressPercentage,
            timeRemaining: trialData.timeRemaining,
          });
        }
      });

      return map;
    },
    enabled: accountIds.length > 0,
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  return {
    trialInfoMap: trialDataMap || new Map(),
    isLoading,
    error: error as Error | null,
    refetch: () => {
      setRefetchTrigger(prev => prev + 1);
    },
  };
}
