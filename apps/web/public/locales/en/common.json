{"homeTabLabel": "Home", "homeTabDescription": "Welcome to your home page", "accountMembers": "Team Members", "membersTabDescription": "Here you can manage the members of your team.", "billingTabLabel": "Billing", "billingTabDescription": "Manage your billing and subscription", "dashboardTabLabel": "Dashboard", "settingsTabLabel": "Settings", "profileSettingsTabLabel": "Profile", "subscriptionSettingsTabLabel": "Subscription", "dashboardTabDescription": "An overview of your account's activity and performance across all your projects.", "settingsTabDescription": "Manage your settings and preferences.", "emailAddress": "Email Address", "password": "Password", "modalConfirmationQuestion": "Are you sure you want to continue?", "imageInputLabel": "Click here to upload an image", "cancel": "Cancel", "clear": "Clear", "notFound": "Not Found", "backToHomePage": "Back to Home Page", "goBack": "Go Back", "genericServerError": "Sorry, something went wrong.", "genericServerErrorHeading": "Sorry, something went wrong while processing your request. Please contact us if the issue persists.", "pageNotFound": "Sorry, this page does not exist.", "pageNotFoundSubHeading": "Apologies, the page you were looking for was not found", "genericError": "Sorry, something went wrong.", "genericErrorSubHeading": "Apologies, an error occurred while processing your request. Please contact us if the issue persists.", "anonymousUser": "Anonymous", "tryAgain": "Try Again", "theme": "Theme", "lightTheme": "Light", "darkTheme": "Dark", "systemTheme": "System", "expandSidebar": "Expand Sidebar", "collapseSidebar": "Collapse Sidebar", "documentation": "Documentation", "getStarted": "Get Started", "getStartedWithPlan": "Get Started with {{plan}}", "retry": "Retry", "contactUs": "Contact Us", "loading": "Loading. Please wait...", "yourAccounts": "Your Accounts", "continue": "Continue", "skip": "<PERSON><PERSON>", "recommended": "Recommended", "signedInAs": "Signed in as", "pageOfPages": "Page {{page}} of {{total}}", "noData": "No data available", "pageNotFoundHeading": "Ouch! :|", "errorPageHeading": "Ouch! :|", "notifications": "Notifications", "noNotifications": "No notifications", "justNow": "Just now", "newVersionAvailable": "New version available", "newVersionAvailableDescription": "A new version of the app is available. It is recommended to refresh the page to get the latest updates and avoid any issues.", "newVersionSubmitButton": "Reload and Update", "back": "Back", "marketResearch": "Market Research", "marketResearchDescription": "Generate content ideas and research insights for your campaigns", "routes": {"home": "Home", "account": "Account", "members": "Members", "billing": "Billing", "dashboard": "Dashboard", "brandAssets": "Brand Assets", "brand": "Brand", "settings": "Settings", "profile": "Profile", "application": "Application", "marketResearch": "Market Research", "create": "Create"}, "onboarding": {"title": "Getting Started", "description": "Complete these essential tasks to get the most out of your account.", "bonusTitle": "Bonus Tasks", "optional": "Optional"}, "create": {"topRight": {"title": "Quick Actions", "description": "Coming soon - quick action shortcuts"}, "bottomLeft": {"title": "Recent Activity", "description": "Coming soon - recent activity feed"}, "bottomRight": {"title": "Analytics", "description": "Coming soon - key metrics and insights"}}, "roles": {"owner": {"label": "Owner"}, "member": {"label": "Member"}}, "otp": {"requestVerificationCode": "Request Verification Code", "requestVerificationCodeDescription": "We must verify your identity to continue with this action. We'll send a verification code to the email address {{email}}.", "sendingCode": "Sending Code...", "sendVerificationCode": "Send Verification Code", "enterVerificationCode": "Enter Verification Code", "codeSentToEmail": "We've sent a verification code to the email address {{email}}.", "verificationCode": "Verification Code", "enterCodeFromEmail": "Enter the 6-digit code we sent to your email.", "verifying": "Verifying...", "verifyCode": "Verify Code", "requestNewCode": "Request New Code", "errorSendingCode": "Error sending code. Please try again."}, "cookieBanner": {"title": "Hey, we use cookies 🍪", "description": "This website uses cookies to ensure you get the best experience on our website.", "reject": "Reject", "accept": "Accept"}}