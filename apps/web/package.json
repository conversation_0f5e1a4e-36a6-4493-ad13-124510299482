{"name": "web", "version": "0.1.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"analyze": "ANALYZE=true pnpm run build", "build": "next build", "build:test": "NODE_ENV=test next build --turbopack", "clean": "git clean -xdf .next .turbo node_modules", "dev": "next dev --turbo | pino-pretty -c", "lint": "eslint .", "lint:fix": "next lint --fix", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "start": "next start", "start:test": "NODE_ENV=test next start", "typecheck": "tsc --noEmit", "supabase": "supabase", "supabase:start": "supabase status || supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:db:diff": "supabase db diff", "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts", "supabase:db:dump:local": "supabase db dump --local --data-only"}, "dependencies": {"@ai-sdk/openai": "^1.3.21", "@blocknote/core": "^0.33.0", "@blocknote/mantine": "^0.33.0", "@blocknote/react": "^0.33.0", "@blocknote/xl-ai": "^0.33.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@hello-pangea/dnd": "^16.6.0", "@hocuspocus/provider": "^2.15.2", "@hookform/resolvers": "^5.0.1", "@kit/accounts": "workspace:*", "@kit/admin": "workspace:*", "@kit/analytics": "workspace:*", "@kit/auth": "workspace:*", "@kit/billing": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/cms": "workspace:*", "@kit/database-webhooks": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/i18n": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/next": "workspace:*", "@kit/notifications": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/ui": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.10", "@makerkit/data-loader-supabase-nextjs": "^1.2.5", "@marsidev/react-turnstile": "^1.1.0", "@nosecone/next": "1.0.0-beta.6", "@pqina/pintura": "^8.92.16", "@pqina/react-pintura": "^9.0.4", "@radix-ui/react-accordion": "1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "1.2.6", "@react-stately/utils": "^3.10.5", "@remotion/bundler": "v4.0.272", "@remotion/captions": "4.0.272", "@remotion/cli": "4.0.272", "@remotion/cloudrun": "v4.0.272", "@remotion/google-fonts": "4.0.272", "@remotion/lambda": "4.0.272", "@remotion/media-parser": "^4.0.286", "@remotion/player": "v4.0.272", "@remotion/renderer": "v4.0.272", "@remotion/studio": "v4.0.272", "@rocicorp/zero": "0.21.2025062401", "@supabase/supabase-js": "2.49.4", "@tanstack/react-query": "5.75.2", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-bullet-list": "^2.12.0", "@tiptap/extension-list-item": "^2.12.0", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/lodash": "^4.17.16", "@vercel/blob": "^1.1.1", "axios": "^1.7.9", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "elevenlabs": "^1.56.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.7", "gray-matter": "^4.0.3", "input-otp": "^1.4.2", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "langfuse": "^3.37.6", "linkedin-api-client": "^0.3.0", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "motion": "^12.5.0", "next": "15.3.1", "next-sitemap": "^4.2.3", "next-themes": "0.4.6", "openai": "^4.85.4", "posthog-js": "^1.236.7", "react": "19.1.0", "react-best-gradient-color-picker": "^3.0.14", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-confetti": "^6.4.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.2", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.3", "remotion": "4.0.272", "rxjs": "^7.8.1", "short-unique-id": "^5.2.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "twitter-v2": "^1.1.0", "use-company-info": "link:@/app/hooks/use-company-info", "uuid": "^11.1.0", "vaul": "^1.1.2", "y-protocols": "^1.0.6", "yjs": "^13.6.23", "zod": "^3.24.4"}, "pnpm": {"onlyBuiltDependencies": ["@rocicorp/zero-sqlite3"]}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@next/bundle-analyzer": "15.3.1", "@tailwindcss/postcss": "^4.1.5", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.9", "@types/react": "19.1.2", "@types/react-dom": "19.1.3", "babel-plugin-react-compiler": "19.1.0-rc.1", "cssnano": "^7.0.6", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "supabase": "^2.31.8", "tailwindcss": "4.1.5", "typescript": "^5.8.3"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"]}