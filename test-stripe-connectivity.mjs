import { config } from 'dotenv';

// Load environment variables
config();

// Simple test using the same logic as the connectivity service
async function testStripeConnectivity() {
  try {
    console.log('🔍 Testing Stripe connectivity using project logic...');
    
    // Check if environment variables are set
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY environment variable is not set');
    }
    
    console.log('✅ Environment variables loaded');
    console.log('🔑 Using key starting with:', process.env.STRIPE_SECRET_KEY.substring(0, 7) + '...');
    
    // Test using the same method as the connectivity service
    const url = 'https://api.stripe.com';
    const request = await fetch(`${url}/v1/prices`, {
      headers: {
        Authorization: `Bearer ${process.env.STRIPE_SECRET_KEY}`,
      },
    });

    if (!request.ok) {
      const errorText = await request.text();
      throw new Error(`Failed to connect to Stripe. Status: ${request.status}, Response: ${errorText}`);
    }

    const data = await request.json();
    console.log('✅ Stripe API connection successful!');
    console.log('📊 Response status:', request.status);
    console.log('📊 Found', data.data?.length || 0, 'prices');
    
    // Test webhook endpoints
    console.log('📡 Testing webhook endpoints...');
    const webhookRequest = await fetch(`${url}/v1/webhook_endpoints`, {
      headers: {
        Authorization: `Bearer ${process.env.STRIPE_SECRET_KEY}`,
      },
    });

    if (!webhookRequest.ok) {
      const errorText = await webhookRequest.text();
      throw new Error(`Failed to connect to Stripe webhooks. Status: ${webhookRequest.status}, Response: ${errorText}`);
    }

    const webhookData = await webhookRequest.json();
    console.log('✅ Webhook endpoints accessible!');
    console.log('📊 Found', webhookData.data?.length || 0, 'webhook endpoints');
    
    // Test account details
    console.log('📡 Testing account details...');
    const accountRequest = await fetch(`${url}/v1/account`, {
      headers: {
        Authorization: `Bearer ${process.env.STRIPE_SECRET_KEY}`,
      },
    });

    if (!accountRequest.ok) {
      const errorText = await accountRequest.text();
      throw new Error(`Failed to get account details. Status: ${accountRequest.status}, Response: ${errorText}`);
    }

    const accountData = await accountRequest.json();
    console.log('✅ Account details accessible!');
    console.log('📊 Account ID:', accountData.id);
    console.log('📊 Account type:', accountData.type);
    console.log('📊 Charges enabled:', accountData.charges_enabled);
    console.log('📊 Payouts enabled:', accountData.payouts_enabled);
    
    console.log('\n🎉 All Stripe connectivity tests passed!');
    console.log('✅ Your Stripe API connection is working properly.');
    
  } catch (error) {
    console.error('❌ Stripe connectivity test failed:');
    console.error('Error message:', error.message);
    
    if (error.message.includes('401')) {
      console.error('🔑 Authentication failed. Please check your STRIPE_SECRET_KEY.');
      console.error('💡 Make sure your key starts with sk_test_ or sk_live_');
    } else if (error.message.includes('403')) {
      console.error('🚫 Permission denied. Please check your API key permissions.');
    } else if (error.message.includes('STRIPE_SECRET_KEY environment variable is not set')) {
      console.error('📝 Please create a .env.local file with your Stripe credentials.');
      console.error('💡 Example:');
      console.error('   STRIPE_SECRET_KEY=sk_test_your_key_here');
      console.error('   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here');
      console.error('   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here');
    }
    
    process.exit(1);
  }
}

// Run the test
testStripeConnectivity(); 